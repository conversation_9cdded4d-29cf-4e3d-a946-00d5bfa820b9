<template>
  <el-dialog
    title="模板配置管理"
    :visible.sync="dialogVisible"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="template-config">
      <!-- 分类选择 -->
      <div class="category-selector">
        <el-form :inline="true" size="small">
          <el-form-item label="选择分类:">
            <el-cascader
              v-model="selectedCategoryPath"
              :options="categoryTree"
              :props="categoryProps"
              placeholder="请选择分类"
              style="width: 300px;"
              clearable
              @change="handleCategoryChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :disabled="!selectedCategoryId" @click="loadTemplates">
              加载模板
            </el-button>
            <el-button type="success" :disabled="!selectedCategoryId" @click="createNewTemplate">
              新建模板
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 模板列表 -->
      <div v-if="templates.length > 0" class="template-list">
        <el-table :data="templates" border size="small">
          <el-table-column prop="templateName" label="模板名称" width="200" />
          <el-table-column prop="templateDescription" label="模板描述" min-width="200" />
          <el-table-column prop="isDefault" label="默认模板" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isDefault ? 'success' : 'info'">
                {{ scope.row.isDefault ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isActive" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
                {{ scope.row.isActive ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button size="mini" @click="editTemplate(scope.row)">编辑</el-button>
              <el-button size="mini" type="warning" @click="copyTemplate(scope.row)">复制</el-button>
              <el-button size="mini" type="danger" @click="deleteTemplate(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 模板编辑区域 -->
      <div v-if="currentTemplate" class="template-editor">
        <el-divider content-position="left">模板配置</el-divider>
        
        <el-form ref="templateForm" :model="currentTemplate" :rules="templateRules" label-width="120px" size="small">
          <!-- 基本信息 -->
          <el-card class="config-section" shadow="never">
            <div slot="header">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="模板名称" prop="templateName">
                  <el-input v-model="currentTemplate.templateName" placeholder="请输入模板名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="模板描述" prop="templateDescription">
                  <el-input v-model="currentTemplate.templateDescription" placeholder="请输入模板描述" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否默认">
                  <el-switch v-model="currentTemplate.isDefault" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否启用">
                  <el-switch v-model="currentTemplate.isActive" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- 显示字段配置 -->
          <el-card class="config-section" shadow="never">
            <div slot="header" class="section-header">
              <span>显示字段配置</span>
              <el-button size="mini" type="text" @click="addDisplayField">
                <i class="el-icon-plus"></i> 添加字段
              </el-button>
            </div>
            
            <el-table :data="currentTemplate.displayFields" border size="small">
              <el-table-column label="字段键名" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldKey" placeholder="字段键名" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="显示标签" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldLabel" placeholder="显示标签" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="字段类型" width="100">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.fieldType" size="mini">
                    <el-option label="文本" value="text" />
                    <el-option label="数字" value="number" />
                    <el-option label="选择" value="select" />
                    <el-option label="日期" value="date" />
                    <el-option label="布尔" value="boolean" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="列宽" width="80">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.width" :min="50" :max="500" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="必填" width="60">
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.isRequired" />
                </template>
              </el-table-column>
              <el-table-column label="显示" width="60">
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.isVisible" />
                </template>
              </el-table-column>
              <el-table-column label="可编辑" width="70">
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.isEditable" />
                </template>
              </el-table-column>
              <el-table-column label="选项" min-width="150">
                <template slot-scope="scope">
                  <el-input
                    v-if="scope.row.fieldType === 'select'"
                    v-model="scope.row.optionsText"
                    placeholder="用逗号分隔选项"
                    size="mini"
                    @blur="updateOptions(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeDisplayField(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <!-- 自定义字段配置 -->
          <el-card class="config-section" shadow="never">
            <div slot="header" class="section-header">
              <span>自定义字段配置</span>
              <el-button size="mini" type="text" @click="addCustomField">
                <i class="el-icon-plus"></i> 添加字段
              </el-button>
            </div>
            
            <el-table :data="currentTemplate.customFields" border size="small">
              <el-table-column label="字段键名" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldKey" placeholder="字段键名" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="显示标签" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldLabel" placeholder="显示标签" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="字段类型" width="100">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.fieldType" size="mini">
                    <el-option label="文本" value="text" />
                    <el-option label="数字" value="number" />
                    <el-option label="选择" value="select" />
                    <el-option label="多选" value="multiSelect" />
                    <el-option label="日期" value="date" />
                    <el-option label="布尔" value="boolean" />
                    <el-option label="文本域" value="textarea" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="必填" width="60">
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.isRequired" />
                </template>
              </el-table-column>
              <el-table-column label="单位" width="80">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.unit" placeholder="单位" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="选项" min-width="150">
                <template slot-scope="scope">
                  <el-input
                    v-if="['select', 'multiSelect'].includes(scope.row.fieldType)"
                    v-model="scope.row.optionsText"
                    placeholder="用逗号分隔选项"
                    size="mini"
                    @blur="updateOptions(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="描述" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.description" placeholder="字段描述" size="mini" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeCustomField(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-form>

        <!-- 保存按钮 -->
        <div class="save-actions">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveTemplate" :loading="saving">保存模板</el-button>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTemplatesByCategory, saveTemplate } from '@/api/index';

export default {
  name: 'TemplateConfigDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    categoryTree: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      saving: false,
      selectedCategoryPath: [],
      selectedCategoryId: '',
      templates: [],
      currentTemplate: null,
      
      // 分类属性
      categoryProps: {
        value: '_id',
        label: 'name',
        children: 'children',
        checkStrictly: true
      },

      // 表单验证规则
      templateRules: {
        templateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  methods: {
    // 分类选择变化
    handleCategoryChange(value) {
      if (value && value.length > 0) {
        this.selectedCategoryId = value[value.length - 1];
      } else {
        this.selectedCategoryId = '';
      }
      this.templates = [];
      this.currentTemplate = null;
    },

    // 加载模板列表
    async loadTemplates() {
      try {
        const response = await getTemplatesByCategory({
          categoryId: this.selectedCategoryId
        });
        if (response.status === 200) {
          this.templates = response.data;
        }
      } catch (error) {
        this.$message.error('加载模板失败: ' + error.message);
      }
    },

    // 新建模板
    createNewTemplate() {
      this.currentTemplate = {
        categoryId: this.selectedCategoryId,
        templateName: '',
        templateDescription: '',
        isDefault: false,
        isActive: true,
        displayFields: [],
        customFields: []
      };
    },

    // 编辑模板
    editTemplate(template) {
      this.currentTemplate = JSON.parse(JSON.stringify(template));
      
      // 处理选项文本
      this.currentTemplate.displayFields.forEach(field => {
        if (field.options && field.options.length > 0) {
          field.optionsText = field.options.join(',');
        }
      });
      
      this.currentTemplate.customFields.forEach(field => {
        if (field.options && field.options.length > 0) {
          field.optionsText = field.options.join(',');
        }
      });
    },

    // 复制模板
    copyTemplate(template) {
      const newTemplate = JSON.parse(JSON.stringify(template));
      newTemplate._id = undefined;
      newTemplate.templateName = template.templateName + '_副本';
      newTemplate.isDefault = false;
      this.editTemplate(newTemplate);
    },

    // 删除模板
    async deleteTemplate(template) {
      try {
        await this.$confirm('确定要删除这个模板吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        // 这里应该调用删除API
        this.$message.success('删除成功');
        this.loadTemplates();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message);
        }
      }
    },

    // 添加显示字段
    addDisplayField() {
      this.currentTemplate.displayFields.push({
        fieldKey: '',
        fieldLabel: '',
        fieldType: 'text',
        width: 120,
        isRequired: false,
        isVisible: true,
        isEditable: true,
        options: [],
        optionsText: ''
      });
    },

    // 移除显示字段
    removeDisplayField(index) {
      this.currentTemplate.displayFields.splice(index, 1);
    },

    // 添加自定义字段
    addCustomField() {
      this.currentTemplate.customFields.push({
        fieldKey: '',
        fieldLabel: '',
        fieldType: 'text',
        isRequired: false,
        unit: '',
        options: [],
        optionsText: '',
        description: ''
      });
    },

    // 移除自定义字段
    removeCustomField(index) {
      this.currentTemplate.customFields.splice(index, 1);
    },

    // 更新选项
    updateOptions(field) {
      if (field.optionsText) {
        field.options = field.optionsText.split(',').map(opt => opt.trim()).filter(opt => opt);
      } else {
        field.options = [];
      }
    },

    // 保存模板
    async saveTemplate() {
      this.$refs.templateForm.validate(async (valid) => {
        if (valid) {
          this.saving = true;
          try {
            // 处理选项数据
            this.currentTemplate.displayFields.forEach(field => {
              this.updateOptions(field);
              delete field.optionsText;
            });
            
            this.currentTemplate.customFields.forEach(field => {
              this.updateOptions(field);
              delete field.optionsText;
            });

            const response = await saveTemplate(this.currentTemplate);
            if (response.status === 200) {
              this.$message.success('保存成功');
              this.currentTemplate = null;
              this.loadTemplates();
            }
          } catch (error) {
            this.$message.error('保存失败: ' + error.message);
          } finally {
            this.saving = false;
          }
        }
      });
    },

    // 取消编辑
    cancelEdit() {
      this.currentTemplate = null;
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
      this.selectedCategoryPath = [];
      this.selectedCategoryId = '';
      this.templates = [];
      this.currentTemplate = null;
    }
  }
};
</script>

<style scoped>
.template-config {
  max-height: 600px;
  overflow-y: auto;
}

.category-selector {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.template-list {
  margin-bottom: 20px;
}

.template-editor {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.config-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.save-actions {
  text-align: right;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer {
  text-align: right;
}
</style>
