<template>
  <el-dialog
    :title="isEdit ? '编辑产品' : '新增产品'"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="productForm"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      size="small"
    >
      <!-- 基础信息 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>基础信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="product">
              <el-input v-model="formData.product" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料编码" prop="materialCode">
              <el-input v-model="formData.materialCode" placeholder="请输入物料编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="型号" prop="modelNumber">
              <el-input v-model="formData.modelNumber" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品规格" prop="productSpec">
              <el-input v-model="formData.productSpec" placeholder="请输入产品规格" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-cascader
                v-model="selectedCategoryPath"
                :options="categoryTree"
                :props="categoryProps"
                placeholder="请选择分类"
                style="width: 100%;"
                clearable
                @change="handleCategoryChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库" prop="warehouseId">
              <el-select v-model="formData.warehouseId" placeholder="请选择仓库" style="width: 100%;">
                <el-option
                  v-for="warehouse in warehouseList"
                  :key="warehouse._id"
                  :label="warehouse.name"
                  :value="warehouse._id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="库存数量" prop="surplus">
              <el-input-number
                v-model="formData.surplus"
                :min="0"
                placeholder="请输入库存数量"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂家" prop="vender">
              <el-input v-model="formData.vender" placeholder="请输入厂家" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 防护属性 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>防护属性</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="防护类型" prop="protectionType">
              <el-input v-model="formData.protectionType" placeholder="请输入防护类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="防护用途" prop="function">
              <el-input v-model="formData.function" placeholder="请输入防护用途" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="危害因素" prop="harmFactors">
          <el-select
            v-model="formData.harmFactors"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入危害因素"
            style="width: 100%;"
          >
            <el-option
              v-for="factor in commonHarmFactors"
              :key="factor"
              :label="factor"
              :value="factor"
            />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 自定义属性 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>自定义属性</span>
          <el-button size="mini" type="text" @click="addCustomAttribute">
            <i class="el-icon-plus"></i> 添加属性
          </el-button>
        </div>
        
        <div v-if="formData.customAttributes.length === 0" class="empty-custom-attrs">
          暂无自定义属性，点击上方"添加属性"按钮添加
        </div>

        <div v-for="(attr, index) in formData.customAttributes" :key="index" class="custom-attr-item">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input v-model="attr.key" placeholder="属性键" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="attr.label" placeholder="显示标签" />
            </el-col>
            <el-col :span="4">
              <el-select v-model="attr.dataType" placeholder="类型">
                <el-option label="文本" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔" value="boolean" />
                <el-option label="数组" value="array" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="attr.value" placeholder="属性值" />
            </el-col>
            <el-col :span="2">
              <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeCustomAttribute(index)" />
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 其他信息 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span>其他信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="特点" prop="characteristic">
              <el-input
                v-model="formData.characteristic"
                type="textarea"
                :rows="3"
                placeholder="请输入产品特点"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用环境" prop="industryEnvironment">
              <el-input
                v-model="formData.industryEnvironment"
                type="textarea"
                :rows="3"
                placeholder="请输入使用行业或环境"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="formData.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProductEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    productData: {
      type: Object,
      default: null
    },
    categoryTree: {
      type: Array,
      default: () => []
    },
    warehouseList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      saving: false,
      selectedCategoryPath: [],
      
      // 分类属性
      categoryProps: {
        value: '_id',
        label: 'name',
        children: 'children',
        checkStrictly: true
      },

      // 常见危害因素
      commonHarmFactors: [
        '粉尘', '有毒气体', '缺氧', '噪声', '高温', '低温',
        '化学腐蚀', '机械伤害', '电击', '辐射', '生物危害'
      ],

      // 表单数据
      formData: {
        _id: '',
        product: '',
        materialCode: '',
        modelNumber: '',
        productSpec: '',
        categoryId: '',
        warehouseId: '',
        surplus: 0,
        vender: '',
        protectionType: '',
        function: '',
        harmFactors: [],
        characteristic: '',
        industryEnvironment: '',
        remark: '',
        isActive: true,
        customAttributes: []
      },

      // 表单验证规则
      formRules: {
        product: [
          { required: true, message: '请输入产品名称', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        warehouseId: [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    isEdit() {
      return this.productData && this.productData._id;
    }
  },
  watch: {
    productData: {
      handler(newVal) {
        if (newVal) {
          this.initFormData(newVal);
        } else {
          this.resetFormData();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData(data) {
      this.formData = {
        ...this.formData,
        ...data,
        customAttributes: data.customAttributes || []
      };
      
      // 设置分类路径
      if (data.categoryId) {
        this.selectedCategoryPath = this.buildCategoryPath(data.categoryId);
      }
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        _id: '',
        product: '',
        materialCode: '',
        modelNumber: '',
        productSpec: '',
        categoryId: '',
        warehouseId: '',
        surplus: 0,
        vender: '',
        protectionType: '',
        function: '',
        harmFactors: [],
        characteristic: '',
        industryEnvironment: '',
        remark: '',
        isActive: true,
        customAttributes: []
      };
      this.selectedCategoryPath = [];
    },

    // 构建分类路径
    buildCategoryPath(categoryId) {
      const path = [];
      const findPath = (tree, targetId, currentPath = []) => {
        for (const node of tree) {
          const newPath = [...currentPath, node._id];
          if (node._id === targetId) {
            path.push(...newPath);
            return true;
          }
          if (node.children && node.children.length > 0) {
            if (findPath(node.children, targetId, newPath)) {
              return true;
            }
          }
        }
        return false;
      };
      findPath(this.categoryTree, categoryId);
      return path;
    },

    // 分类选择变化
    handleCategoryChange(value) {
      if (value && value.length > 0) {
        this.formData.categoryId = value[value.length - 1];
        // 设置分类名称和路径
        const categoryInfo = this.findCategoryInfo(this.categoryTree, this.formData.categoryId);
        if (categoryInfo) {
          this.formData.categoryName = categoryInfo.name;
          this.formData.categoryPath = categoryInfo.path;
        }
      } else {
        this.formData.categoryId = '';
        this.formData.categoryName = '';
        this.formData.categoryPath = '';
      }
    },

    // 查找分类信息
    findCategoryInfo(tree, id) {
      for (const node of tree) {
        if (node._id === id) {
          return { name: node.name, path: node.path };
        }
        if (node.children && node.children.length > 0) {
          const found = this.findCategoryInfo(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    // 添加自定义属性
    addCustomAttribute() {
      this.formData.customAttributes.push({
        key: '',
        label: '',
        value: '',
        dataType: 'string',
        required: false
      });
    },

    // 移除自定义属性
    removeCustomAttribute(index) {
      this.formData.customAttributes.splice(index, 1);
    },

    // 保存
    handleSave() {
      this.$refs.productForm.validate((valid) => {
        if (valid) {
          this.saving = true;
          
          // 清理空的自定义属性
          this.formData.customAttributes = this.formData.customAttributes.filter(
            attr => attr.key && attr.label
          );
          
          this.$emit('save', { ...this.formData });
        }
      });
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
      this.saving = false;
      this.$refs.productForm.resetFields();
    }
  }
};
</script>

<style scoped>
.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.custom-attr-item {
  margin-bottom: 10px;
}

.empty-custom-attrs {
  text-align: center;
  color: #999;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
