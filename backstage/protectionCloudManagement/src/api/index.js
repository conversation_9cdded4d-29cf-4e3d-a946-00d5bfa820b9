import request from '@root/publicMethods/request';
// import { method } from 'lodash';

// 获取当前分支
export function getBranch(data) {
  return request({
    url: '/api/adminorg/getBranch',
    data,
    method: 'get',
  });
}

export function findMillConstruction(data) {
  return request({
    url: '/manage/millConstruction/findMillConstruction',
    method: 'post',
    data,
  });
}


export function saveDefendProductList(data) { // 保存防护用品清单
  return request({
    url: '/api/defendproducts/saveDefendProductList',
    method: 'post',
    data,
  });
}

export function getDefendProductList(data) { // 获取清单
  return request({
    url: '/api/defendproducts/getDefendProductList',
    method: 'post',
    data,
  });
}

export function saveProtectionPlan(data) { // 保存防护用品发放计划
  return request({
    url: '/api/defendproducts/saveProtectionPlan',
    method: 'post',
    data,
  });
}


export function getProtectionPlan(data) { // 获取防护用品发放计划
  return request({
    url: '/api/defendproducts/getProtectionPlan',
    method: 'post',
    data,
  });
}

export function saveOneprotection(data) { // 保存单个防护用品信息
  return request({
    url: '/api/defendproducts/saveOneprotection',
    method: 'post',
    data,
  });
}

export function delOneprotection(data) { // 删除单个防护用品信息
  return request({
    url: '/api/defendproducts/saveOneprotection',
    method: 'delete',
    data,
  });
}

export function savePageInfo(params) { // 保存单个分类名称与设置
  return request({
    url: '/api/defendproducts/savePageInfo',
    method: 'get',
    params,
  });
}


export function saveSingle(data) { // 保存单个计划
  return request({
    url: '/api/defendproducts/saveSingle',
    method: 'post',
    data,
  });
}

export function delCategory(params) { // 删除防护用品清单的一个分类
  return request({
    url: '/api/defendproducts/delCategory',
    method: 'get',
    params,
  });
}

export function immediatePPEPlan(params) { // 立即执行防护用品发放计划
  return request({
    url: '/api/defendproducts/immediatePPEPlan',
    method: 'get',
    params,
  });
}

export function delProtectionPlan(data) { // 删除防护计划
  return request({
    url: '/api/defendproducts/protectionPlanCrud',
    method: 'delete',
    data,
  });
}

export function getScrapProducts(data) { // 获取报废用品列表
  return request({
    url: '/manage/scrapProduct/getScrapProducts',
    method: 'post',
    data,
  });
}


export function addProtectionPlan(data) { // 新增防护计划
  return request({
    url: '/api/defendproducts/protectionPlanCrud',
    method: 'post',
    data,
  });
}

export function confirmScrap(params) { // 确认防护用品报废
  return request({
    url: '/manage/scrapProduct/confirmScrap',
    method: 'get',
    params,
  });
}

export function deleteScrapRecord(data) { // 删除报废用品记录
  return request({
    url: '/manage/scrapProduct/deleteScrapRecord',
    method: 'post',
    data,
  });
}

export function exportByMonth(data) { // 按月度导出记录
  return request({
    url: '/manage/scrapProduct/exportByMonth',
    method: 'post',
    data,
  });
}

export function getApplicationProducts(data) { // 获取申请列表
  return request({
    url: '/api/defendproducts/getApplicationProducts',
    method: 'post',
    data,
  });
}

export function selectApplication(data) { // 改变申请状态
  return request({
    url: '/api/defendproducts/selectApplication',
    method: 'post',
    data,
  });
}

export function findHarmFactors(params) { // 获取危害因素
  return request({
    url: '/manage/jobHealth/findHarmFactors',
    method: 'get',
    params,
  });
}

export function adminResourceList(params) {
  return request({
    url: '/manage/employees/getList',
    method: 'get',
    params,
  });
}

export function getRadioButtonEnabled(params) {
  return request({
    url: '/manage/adminResource/getRadioButtonEnabled',
    method: 'get',
    params,
  });
}

// ==================== 仓库管理相关API ====================

// 获取用户可访问的仓库列表
export function getWarehouseList(params) {
  return request({
    url: '/manage/warehouse/list',
    method: 'get',
    params,
  });
}

// ==================== 扁平化防护用品标准管理API ====================

// 获取扁平化防护用品标准列表
export function getFlatStandardsList(data) {
  return request({
    url: '/api/defendproducts/getFlatStandardsList',
    method: 'post',
    data,
  });
}

// 获取筛选选项数据
export function getFilterOptions() {
  return request({
    url: '/api/defendproducts/getFilterOptions',
    method: 'get',
  });
}

// 批量更新配置状态
export function updateConfigStatus(data) {
  return request({
    url: '/api/defendproducts/updateConfigStatus',
    method: 'post',
    data,
  });
}

// 获取工种详细信息
export function getWorkStationDetail(data) {
  return request({
    url: '/api/defendproducts/getWorkStationDetail',
    method: 'post',
    data,
  });
}

// ==================== 新增：防护用品产品管理API ====================

// 获取产品列表
export function getProductList(data) {
  return request({
    url: '/api/defendproducts/getProductList',
    method: 'post',
    data
  });
}

// 保存产品
export function saveProduct(data) {
  return request({
    url: '/api/defendproducts/saveProduct',
    method: 'post',
    data
  });
}

// 删除产品
export function deleteProducts(data) {
  return request({
    url: '/api/defendproducts/deleteProducts',
    method: 'post',
    data
  });
}

// 获取导入模板配置
export function getImportTemplate(params) {
  return request({
    url: '/api/defendproducts/getImportTemplate',
    method: 'get',
    params
  });
}

// 下载导入模板
export function downloadTemplate(params) {
  return request({
    url: '/api/defendproducts/downloadTemplate',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 解析导入文件
export function parseImportFile(file, categoryId, warehouseId, options = {}) {
  const formData = new FormData();
  formData.append('file', file);

  // 总是添加categoryId，即使是空字符串
  formData.append('categoryId', categoryId || '');

  // 总是添加warehouseId
  formData.append('warehouseId', warehouseId || '');

  // 总是添加智能解析选项，修复undefined问题
  formData.append('intelligentParsing', options.intelligentParsing ? 'true' : 'false');

  return request({
    url: '/api/defendproducts/parseImportFile',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 导入产品
export function importProducts(data) {
  return request({
    url: '/api/defendproducts/importProducts',
    method: 'post',
    data
  });
}

// 获取分类模板列表
export function getTemplatesByCategory(params) {
  return request({
    url: '/api/defendproducts/getTemplatesByCategory',
    method: 'get',
    params
  });
}

// 保存模板配置
export function saveTemplate(data) {
  return request({
    url: '/api/defendproducts/saveTemplate',
    method: 'post',
    data
  });
}


