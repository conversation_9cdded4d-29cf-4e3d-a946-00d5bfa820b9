{"name": "qy", "version": "1.0.1423", "description": "企业职业健康数字化管理系统", "private": true, "egg": {"declarations": true}, "dependencies": {"@alicloud/pop-core": "^1.7.12", "@koa/multer": "^3.0.2", "@riophae/vue-treeselect": "^0.4.0", "@zyws/egg-dora-uploadfile": "^1.0.2", "adm-zip": "^0.5.16", "amqplib": "^0.10.4", "angular-expressions": "^1.4.3", "archiver": "^5.3.1", "await-stream-ready": "^1.0.1", "axios": "^1.7.9", "baidu-aip-sdk": "^4.0.0", "ckeditor4-vue": "^2.2.0", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "date-fns": "^3.2.0", "dingtalk-encrypt": "^2.1.1", "docxtemplater": "^3.27.1", "docxtemplater-image-module-free-norepeat": "^1.0.8", "driver.js": "^0.9.8", "egg": "^3.12.0", "egg-alinode": "^2.0.1", "egg-dora-validate": "^1.0.3", "egg-mongoose": "^3.3.1", "egg-redis": "^2.6.0", "egg-scripts": "^2.17.0", "egg-socket.io": "^4.1.6", "egg-view-nunjucks": "^2.2.0", "eslint-config-egg": "^12.2.1", "file-saver": "^2.0.5", "follow-redirects": "^1.15.6", "global-agent": "^3.0.0", "html2canvas": "^1.4.1", "http-proxy": "^1.18.1", "id-validator-modify": "^1.0.3", "js-cache": "^1.0.3", "jsonwebtoken": "9.0.0", "jspdf": "^2.5.1", "jszip-utils": "^0.1.0", "koa-compress": "^5.0.0", "lodash": "^4.17.21", "mammoth": "^1.4.16", "md5": "^2.2.1", "minio": "^7.1.3", "mkdirp": "^3.0.1", "module-alias": "^2.2.1", "mongodb": "^5.1.0", "multer": "^1.4.5-lts.2", "muri": "^1.3.0", "nanoid": "^3.3.6", "node-schedule": "^2.1.1", "node-xlsx": "^0.23.0", "pdfkit": "^0.13.0", "pinyin": "^2.11.2", "pizzip": "^3.0.6", "postcss-pxtorem": "6.0.0", "qs": "^6.11.2", "shelljs": "^0.8.5", "shortid": "^2.2.14", "stream-to-buffer": "^0.1.0", "svg-captcha": "^1.4.0", "uri-js": "^4.4.0", "validate": "^5.2.0", "validator": "^13.1.0", "vue-virtual-scroll-list": "^2.3.5", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "xlsx-style": "^0.8.13", "exceljs": "^4.3.0"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.1.0", "docx-preview": "^0.1.15", "egg-bin": "^6.4.1", "egg-ci": "^2.1.0", "egg-mock": "^5.10.0", "eslint": "^8.0.0", "eslint-config-egg": "^7.1.0", "eslint-plugin-vue": "^9.0.0"}, "resolutions": {"axios": "^1.7.9", "braces": "^3.0.3", "cookie": "^1.0.2", "dompurify": "^3.2.4", "cross-spawn": "^7.0.6", "jsrsasign": "^11.1.0", "koa": "^2.15.4", "micromatch": "^4.0.8", "undici": "^5.26.3", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "ws": "^8.18.0"}, "overrides": {"axios": "^1.7.9", "braces": "^3.0.3", "cookie": "^1.0.2", "dompurify": "^3.2.4", "cross-spawn": "^7.0.6", "jsrsasign": "^11.1.0", "koa": "^2.15.4", "micromatch": "^4.0.8", "undici": "^5.26.3", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "ws": "^8.18.0"}, "engines": {"node": ">=14.0.0"}, "scripts": {"start": "cross-env NODE_ENV=production ENABLE_NODE_LOG=YES && egg-scripts start --daemon --workers=2 --title=qy --sticky --stdout=/opt/log/qy/master-stdout.log --stderr=/opt/log/qy/master-stderr.log", "zaw": "cross-env NODE_ENV=production ENABLE_NODE_LOG=YES && egg-scripts start --title=zaw-qy --sticky --env=zaw --stdout=/opt/log/zaw-qy/master-stdout.log --stderr=/opt/log/zaw-qy/master-stderr.log", "sit": "cross-env NODE_ENV=production ENABLE_NODE_LOG=YES && egg-scripts start --daemon --workers=2 --title=qy --sticky --env=sit --stdout=/opt/log/qy/master-stdout.log --stderr=/opt/log/qy/master-stderr.log", "k8s": "egg-scripts start --stikcy --title=$(echo $EGG_SERVER_ENV)-qy --workers=2", "stop": "egg-scripts stop --title=qy", "stop-zaw": "egg-scripts stop --title=zaw-qy", "dev": "cross-env NODE_ENV=development && egg-bin dev", "debug": "egg-bin debug", "build": "npm run lint --  --fix", "test": "npm run lint -- --fix && npm run test-local && npm run cov", "test-local": "cross-env NODE_ENV=test && cross-env EGG_SERVER_ENV=unittest && egg-bin test --timeout 5400", "cov": "cross-env NODE_ENV=test && cross-env EGG_SERVER_ENV=unittest && egg-bin cov", "lint": "eslint .", "ci": "npm run lint -- --fix && npm run cov", "autod": "autod", "initData": "node ./build/restore", "backupData": "node ./build/dump", "init": "node ./install/index.js", "clusterStart": "cross-env NODE_ENV=production && pm2 start server.js --name qy", "clusterStop": "pm2 stop server.js --name qy"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "_moduleAliases": {"@root": ".", "@validate": "app/validate", "@utils": "app/utils"}}