/* eslint valid-jsdoc: "off" */
exports.jk_Defend_Products = {
  alias: 'defendproducts', // 插件目录，必须为英文
  pkgName: 'egg-jk-defendproducts', // 插件包名
  enName: 'jk_Defend_Products', // 插件名
  name: '个人防护用品', // 插件名称
  description: '个人防护用品', // 插件描述
  adminApi: [
    {
      url: 'defendproducts/get',
      method: 'get',
      controllerName: 'get',
      details: '获取防护用品表单',
    },
    {
      url: 'defendproducts/deletefile',
      method: 'post',
      controllerName: 'deletefile',
      details: '删除文件',
    },
    {
      url: 'defendproducts/deleteSelected',
      method: 'post',
      controllerName: 'deletefile',
      details: '批量删除删除文件',
    },
    {
      url: 'defendproducts/deleteTables',
      method: 'post',
      controllerName: 'deleteTables',
      details: '批量删除删除数据',
    },
    {
      url: 'defendproducts/delete',
      method: 'post',
      controllerName: 'delete',
      details: '删除表单',
    },
    {
      url: 'defendproducts/deleteSingleData',
      method: 'post',
      controllerName: 'deleteSingleData',
      details: '删除表单单条数据',
    },
    {
      url: 'defendproducts/updateSingle',
      method: 'post',
      controllerName: 'updateSingle',
      details: '更新单条数据',
    },
    {
      url: 'defendproducts/update',
      method: 'post',
      controllerName: 'update',
      details: '提交表单',
    },
    {
      url: 'defendproducts/addexcel',
      method: 'post',
      controllerName: 'addexcel',
      details: '通过excel导入防护用品登记表',
    },
    {
      url: 'defendproducts/autogenerationProtective',
      method: 'get',
      controllerName: 'autogenerationProtective',
      details: '自动生成防护用品',
    },
    {
      url: 'defendproducts/saveProgrammePlan',
      method: 'post',
      controllerName: 'saveProgrammePlan',
      details: '保存防护用品发放计划',
    },
    {
      url: 'scrapProduct/getScrapProducts',
      method: 'post',
      controllerName: 'getScrapProducts',
      details: '获取防护用品报废列表',
    },
    {
      url: 'scrapProduct/confirmScrap',
      method: 'get',
      controllerName: 'confirmScrap',
      details: '确认防护用品报废',
    },
    {
      url: 'scrapProduct/deleteScrapRecord',
      method: 'post',
      controllerName: 'deleteScrapRecord',
      details: '删除用品报废记录',
    },
    {
      url: 'scrapProduct/exportByMonth',
      method: 'post',
      controllerName: 'exportByMonth',
      details: '按照月度导出报废列表',
    },
    {
      url: 'defendproducts/getHardhatList',
      method: 'get',
      controllerName: 'getHardhatList',
      details: '获取安全帽列表',
    },
    {
      url: 'defendproducts/findExpiredHat',
      method: 'get',
      controllerName: 'findExpiredHat',
      details: '获取过期安全帽列表',
    },
    {
      url: 'defendproducts/createHardhat',
      method: 'post',
      controllerName: 'createHardhat',
      details: '创建安全帽',
    },
    {
      url: 'defendproducts/updateExipreHat',
      method: 'post',
      controllerName: 'updateExipreHat',
      details: '更新过期安全帽',
    },
    {
      url: 'defendproducts/updateHardhat',
      method: 'post',
      controllerName: 'updateHardhat',
      details: '更新安全帽',
    },
    {
      url: 'defendproducts/deleteHardhat',
      method: 'post',
      controllerName: 'deleteHardhat',
      details: '删除安全帽',
    },
    {
      url: 'protectiveStatistics/inventoryStatistics',
      method: 'get',
      controllerName: 'getInventoryStatistics',
      details: '获取防护用品库存统计',
    },
    {
      url: 'protectiveStatistics/usageRanking',
      method: 'post',
      controllerName: 'getUsageRanking',
      details: '获取防护用品人均领用排名',
    },
    {
      url: 'defendproducts/findEmployeesByDepart',
      method: 'get',
      controllerName: 'findEmployeesByDepart',
      details: '获取部门员工',
    },
  ],
  fontApi: [
    {
      url: 'defendproducts/createTime',
      method: 'post',
      controllerName: 'createTime',
      details: '获取自动生成领用表的时间',
    },
    {
      url: 'defendproducts/saveDefendProductList',
      method: 'post',
      controllerName: 'saveDefendProductList',
      details: '保存防护用品清单',
    },
    {
      url: 'defendproducts/getDefendProductList',
      method: 'post',
      controllerName: 'getDefendProductList',
      details: '获取防护用品清单',
    },
    {
      url: 'defendproducts/saveProtectionPlan',
      method: 'post',
      controllerName: 'saveProtectionPlan',
      details: '保存防护用品发放计划',
    },
    {
      url: 'defendproducts/getProtectionPlan',
      method: 'post',
      controllerName: 'getProtectionPlan',
      details: '获取防护用品发放计划',
    },
    {
      url: 'defendproducts/saveOneprotection',
      method: 'post',
      controllerName: 'saveOneprotection',
      details: '保存单个防护用品信息',
    },
    {
      url: 'defendproducts/saveOneprotection',
      method: 'delete',
      controllerName: 'delOneprotection',
      details: '删除单个防护用品信息',
    },
    {
      url: 'defendproducts/savePageInfo',
      method: 'get',
      controllerName: 'savePageInfo',
      details: '保存单个分类名称与设置',
    },
    {
      url: 'defendproducts/saveSingle',
      method: 'post',
      controllerName: 'saveSingle',
      details: '保存单个计划',
    },
    {
      url: 'defendproducts/createWord',
      method: 'post',
      controllerName: 'createWord',
      details: '下载个人防护用品领用清单',
    },
    {
      url: 'defendproducts/replenishment',
      method: 'get',
      controllerName: 'replenishment',
      details: '补货ppe自提柜',
    },
    {
      url: 'defendproducts/delCategory',
      method: 'get',
      controllerName: 'delCategory',
      details: '删除防护用品清单的一个分类',
    },
    {
      url: 'defendproducts/immediatePPEPlan',
      method: 'get',
      controllerName: 'immediatePPEPlan',
      details: '立即执行防护用品发放计划',
    },
    {
      url: 'defendproducts/protectionPlanCrud',
      method: 'delete',
      controllerName: 'delProtectionPlan',
      details: '删除防护计划',
    },
    {
      url: 'defendproducts/protectionPlanCrud',
      method: 'post',
      controllerName: 'addProtectionPlan',
      details: '新增防护用品发放计划',
    },
    {
      url: 'defendproducts/getReceiveRecordList',
      method: 'post',
      controllerName: 'getReceiveRecordList',
      details: '获取领用记录',
    },
    {
      url: 'defendproducts/receiveRecordCrud',
      method: 'delete',
      controllerName: 'delReceiveRecord',
      details: '删除领用记录',
    },
    {
      url: 'defendproducts/getApplicationProducts',
      method: 'post',
      controllerName: 'getApplicationProducts',
      details: '获取申请列表',
    },
    {
      url: 'defendproducts/selectApplication',
      method: 'post',
      controllerName: 'selectApplication',
      details: '改变申请状态',
    },
    {
      url: 'defendproducts/exportReceiveRecords',
      method: 'post',
      controllerName: 'exportReceiveRecords',
      details: '导出领用记录',
    }, // 扁平化防护用品标准管理相关API
    {
      url: 'defendproducts/getFlatStandardsList',
      method: 'post',
      controllerName: 'getFlatStandardsList',
      details: '获取扁平化防护用品标准列表',
    },
    {
      url: 'defendproducts/getStationDetail',
      method: 'post',
      controllerName: 'getStationDetail',
      details: '获取单个工种的配发标准详情',
    },
    {
      url: 'defendproducts/getFilterOptions',
      method: 'get',
      controllerName: 'getFilterOptions',
      details: '获取筛选选项数据',
    },
    {
      url: 'defendproducts/updateConfigStatus',
      method: 'post',
      controllerName: 'updateConfigStatus',
      details: '批量更新配置状态',
    },
    {
      url: 'defendproducts/getWorkStationDetail',
      method: 'post',
      controllerName: 'getWorkStationDetail',
      details: '获取工种详细信息',
    },
    // ==================== 防护用品分类管理 API ====================
    {
      url: 'defendproducts/getProtectionCategoryTree',
      method: 'get',
      controllerName: 'getProtectionCategoryTree',
      details: '获取防护用品分类树',
    },
    {
      url: 'defendproducts/getProtectionCategoryLeaves',
      method: 'post',
      controllerName: 'getProtectionCategoryLeaves',
      details: '获取叶子节点分类列表',
    },
    {
      url: 'defendproducts/createProtectionCategory',
      method: 'post',
      controllerName: 'createProtectionCategory',
      details: '创建防护用品分类',
    },
    {
      url: 'defendproducts/updateProtectionCategory',
      method: 'post',
      controllerName: 'updateProtectionCategory',
      details: '更新防护用品分类',
    },
    {
      url: 'defendproducts/deleteProtectionCategory',
      method: 'post',
      controllerName: 'deleteProtectionCategory',
      details: '删除防护用品分类',
    },
    {
      url: 'defendproducts/batchProtectionCategory',
      method: 'post',
      controllerName: 'batchProtectionCategory',
      details: '批量操作防护用品分类',
    },
    {
      url: 'defendproducts/recommendProtectionCategory',
      method: 'post',
      controllerName: 'recommendProtectionCategory',
      details: '智能推荐防护用品分类',
    },
    {
      url: 'defendproducts/checkProtectionCategoryUsage',
      method: 'post',
      controllerName: 'checkProtectionCategoryUsage',
      details: '检查分类使用情况',
    },
    {
      url: 'defendproducts/findHarmFactors',
      method: 'post',
      controllerName: 'findHarmFactors',
      details: '查找危害因素',
    },
    {
      url: 'defendproducts/importSystemCategories',
      method: 'post',
      controllerName: 'importSystemCategories',
      details: '导入系统默认分类数据',
    },
    {
      url: 'defendproducts/cleanSystemCategories',
      method: 'post',
      controllerName: 'cleanSystemCategories',
      details: '清理系统默认分类数据',
    },
    // Excel导入功能API
    {
      url: 'defendproducts/downloadCategoryTemplate',
      method: 'get',
      controllerName: 'downloadCategoryTemplate',
      details: '下载防护用品分类导入模板',
    },
    {
      url: 'defendproducts/uploadCategoryFile',
      method: 'post',
      controllerName: 'uploadCategoryFile',
      details: '上传并解析Excel文件',
    },
    {
      url: 'defendproducts/previewCategoryData',
      method: 'post',
      controllerName: 'previewCategoryData',
      details: '预览导入数据',
    },
    {
      url: 'defendproducts/importCategoryData',
      method: 'post',
      controllerName: 'importCategoryData',
      details: '开始导入数据',
    },
    {
      url: 'defendproducts/importProgress',
      method: 'get',
      controllerName: 'importProgress',
      details: 'SSE进度推送',
    },
    // 岗位员工领用状态查询API
    {
      url: 'defendproducts/getStationSummaryReceiveStatus',
      method: 'post',
      controllerName: 'getStationSummaryReceiveStatus',
      details: '获取岗位汇总领用状态',
    },
    {
      url: 'defendproducts/getStationDetailReceiveStatus',
      method: 'post',
      controllerName: 'getStationDetailReceiveStatus',
      details: '获取岗位详细员工领用状态',
    },
    // ==================== 新增：防护用品产品管理API ====================
    {
      url: 'defendproducts/getProductList',
      method: 'post',
      controllerName: 'getProductList',
      details: '获取防护用品产品列表',
    },
    {
      url: 'defendproducts/saveProduct',
      method: 'post',
      controllerName: 'saveProduct',
      details: '保存防护用品产品',
    },
    {
      url: 'defendproducts/deleteProducts',
      method: 'post',
      controllerName: 'deleteProducts',
      details: '删除防护用品产品',
    },
    {
      url: 'defendproducts/getImportTemplate',
      method: 'get',
      controllerName: 'getImportTemplate',
      details: '获取导入模板配置',
    },
    {
      url: 'defendproducts/downloadTemplate',
      method: 'get',
      controllerName: 'downloadTemplate',
      details: '下载Excel导入模板',
    },
    {
      url: 'defendproducts/parseImportFile',
      method: 'post',
      controllerName: 'parseImportFile',
      details: '解析导入文件',
    },
    {
      url: 'defendproducts/importProducts',
      method: 'post',
      controllerName: 'importProducts',
      details: '导入防护用品产品',
    },
    {
      url: 'defendproducts/getTemplatesByCategory',
      method: 'get',
      controllerName: 'getTemplatesByCategory',
      details: '获取分类模板列表',
    },
    {
      url: 'defendproducts/saveTemplate',
      method: 'post',
      controllerName: 'saveTemplate',
      details: '保存模板配置',
    },
    {
      url: 'defendproducts/downloadTemplate',
      method: 'get',
      controllerName: 'downloadTemplate',
      details: '下载Excel导入模板',
    },
  ],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_Defend_Products = {\n
        enable: true,\n        package: 'admin',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    defendproductsRouter:{\n
      match: [ctx => ctx.path.startsWith('/manage/admin'), ctx => ctx.path.startsWith('/api/admin'), ctx => ctx.path.startsWith('/api/defendproducts')],,\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  pageSize: 10,
  multipart: {
    fields: '100',
    fileExtensions: ['.jpg', '.png', '.jpeg', '.doc', '.docx', '.pdf'],
  },
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
  uploadPath: '/upload/defendProduct',
};

