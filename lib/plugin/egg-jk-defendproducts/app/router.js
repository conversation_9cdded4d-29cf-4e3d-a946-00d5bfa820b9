/**
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
  const { router, controller } = app;
  router.get('/', controller.home.index);
  router.post('/add', controller.role.add);
  router.put('/add/:id', controller.role.addbyid);
  router.delete('/delete/:id', controller.role.deletebyid);
  router.get('/get', controller.role.get);
  router.get('/getbyid/:id', controller.role.getById);
  // employee
  router.post('/addemployee', controller.employee.addemployee);
  router.put('/addemployee/:id', controller.employee.addbyid);
  router.delete('/deleteemployee/:id', controller.employee.deletebyid);
  router.get('/getemployee', controller.employee.get);
  router.get('/getbyidemployee/:id', controller.employee.getById);
  // middleware
  router.post('/addtomiddleware', controller.home.addtomw);
  router.post('/addtomiddlewarebyid', controller.home.addbyid);
  router.put('/addtomiddlewarebyidya/:id', controller.home.addbyid2);
  router.get('/getmanage', controller.home.getmanage);
  router.get('/getmanagebyid/:id', controller.home.getById);
  router.delete('/deletemw/:id', controller.home.deletebyid);
  // ==================== 原有防护用品管理路由 ====================
  // 提交
  router.post('/adddefendproduct', controller.defendproducts.add);
  // 获取数据
  router.get('/getdefendproduct', controller.defendproducts.get);
  // 更新数据
  router.post('/updatedefendproduct', controller.defendproducts.update);
  // 表格删除
  router.post('/deletedefendproduct', controller.defendproducts.delete);
  // 文件删除
  router.post('/deletefile', controller.defendproducts.deletefile);
  router.post('/upload', controller.defendproducts.add);

  // ==================== 新增：防护用品产品管理API路由 ====================

  // 产品管理
  router.post('/api/defendproducts/getProductList', controller.api.defendproducts.getProductList);
  router.post('/api/defendproducts/saveProduct', controller.api.defendproducts.saveProduct);
  router.post('/api/defendproducts/deleteProducts', controller.api.defendproducts.deleteProducts);

  // 智能导入
  router.get('/api/defendproducts/getImportTemplate', controller.api.defendproducts.getImportTemplate);
  router.get('/api/defendproducts/downloadTemplate', controller.api.defendproducts.downloadTemplate);
  router.post('/api/defendproducts/parseImportFile', controller.api.defendproducts.parseImportFile);
  router.post('/api/defendproducts/importProducts', controller.api.defendproducts.importProducts);

  // 模板管理
  router.get('/api/defendproducts/getTemplatesByCategory', controller.api.defendproducts.getTemplatesByCategory);
  router.post('/api/defendproducts/saveTemplate', controller.api.defendproducts.saveTemplate);

};
