/* eslint-disable jsdoc/require-param */
const path = require('path');
// const shortid = require('shortid');
// const moment = require('moment');

const DefendProductsApiController = {
  async createTime(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.defendproducts.time(params);
    ctx.body =
      res === 500
        ? { code: 500, message: '服务器出错' }
        : { code: 200, status: 200, data: res };
  },

  async saveDefendProductList(ctx) { // 保存防护用品清单
    const { data, importType, warehouseId } = ctx.request.body;
    const message = await ctx.service.defendproducts.saveDefendProductList(data, importType, warehouseId);
    ctx.body = { code: 200, status: 200, data: message };
  },

  async getDefendProductList(ctx) {
    // 获取防护用品清单
    const data = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const res = await ctx.service.defendproducts.getDefendProductList(data);

    if (res) {
      for (let i = 0; i < res.list.length; i++) {
        const items = res.list[i];
        if (items.data) {
          for (let j = 0; j < items.data.length; j++) {
            const item = items.data[j];
            if (item.picture) {
              item.picture = await ctx.helper.concatenatePath({
                path:
                  ctx.app.config.upload_http_path +
                  '/' +
                  EnterpriseID +
                  '/' +
                  item.picture,
              });
            }
          }
        }
      }
    }
    // console.log(res, '将返回前端的数据');

    ctx.body = {
      code: 200,
      status: 200,
      data: res,
    };
  },

  async saveProtectionPlan(ctx) {
    // 保存防护用品发放计划
    const data = ctx.request.body;
    const message = await ctx.service.defendproducts.saveProtectionPlan(data);
    ctx.body = { code: 200, status: 200, data: message };
  },

  async getProtectionPlan(ctx) {
    // 获取防护用品发放计划
    const data = ctx.request.body;
    const stations = data.stations;
    const grantType = data.grantType ? data.grantType : 'mill'; // 计划分为车间和部门两种类型
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';

    // 构建请求参数
    const requestData = {
      page: parseInt(data.page) || 1,
      limit: parseInt(data.limit) || 10,
      grantType,
    };

    // 添加筛选参数
    let station = [];
    if (!stations) {
      station = '';
    } else if (stations.length > 0) {
      station = stations;
    }

    // 调用服务获取数据
    const res = await ctx.service.defendproducts.getProtectionPlan(
      station,
      grantType,
      requestData
    );
    ctx.body = { code: 200, status: 200, data: res, EnterpriseID };
  },
  async saveOneprotection(ctx) {
    const data = ctx.request.body;
    console.log('=== Controller saveOneprotection 调试信息 ===');
    console.log('完整请求体:', JSON.stringify(data, null, 2));
    console.log('data.data:', JSON.stringify(data.data, null, 2));
    console.log('data.data是否存在:', !!data.data);
    console.log('=== Controller 调试信息结束 ===');

    try {
      const res = await ctx.service.defendproducts.saveOneprotection(data.data);
      console.log('Service返回结果:', res);
      ctx.body = { code: 200, status: 200, data: res };
    } catch (error) {
      console.log('Controller捕获到错误:', error);
      ctx.body = { code: 500, status: 500, message: error.message };
    }
  },
  async delOneprotection(ctx) {
    const { categoryId, dataId } = ctx.request.body;
    const res = await ctx.service.defendproducts.delOneprotection({
      categoryId,
      dataId,
    });
    ctx.body = { code: 200, status: 200, data: res };
  },
  async savePageInfo(ctx) {
    const params = ctx.query;
    const res = await ctx.service.defendproducts.savePageInfo(params);
    ctx.body = { code: 200, status: 200, data: res };
  },
  async saveSingle(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.defendproducts.saveSingle(data);
    ctx.body = { code: 200, status: 200, data: res };
  },

  /**
   * 根据条件查询配发标准
   */
  async findByCondition(ctx) {
    try {
      const data = ctx.request.body;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      // 构建查询条件
      const query = { EnterpriseID };

      if (data.nodeFullId) {
        query.nodeFullId = data.nodeFullId;
      }

      const res = await ctx.service.db.find('ProtectionPlan', query);
      ctx.body = { code: 200, status: 200, data: res };
    } catch (error) {
      ctx.logger.error('查询配发标准失败:', error);
      ctx.body = { code: 500, status: 500, message: '查询失败' };
    }
  },

  /**
   * 获取单个工种的配发标准详情（用于编辑配置）
   */
  async getStationDetail(ctx) {
    try {
      const data = ctx.request.body;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      if (!data.fullId) {
        ctx.body = { code: 400, status: 400, message: '缺少工种ID参数' };
        return;
      }

      // 1. 查询工种基本信息
      const station = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
        fullId: data.fullId,
        EnterpriseID,
        level: 'stations',
      });

      if (!station) {
        ctx.body = { code: 404, status: 404, message: '工种不存在' };
        return;
      }

      // 2. 查询该工种的配发标准
      const protectionPlan = await ctx.service.db.findOne('ProtectionPlan', {
        nodeFullId: data.fullId,
        EnterpriseID,
      });

      // 3. 构建返回数据
      const result = {
        _id: station._id,
        fullId: station.fullId,
        name: station.name,
        level: station.level,
        parentId: station.parentId,
        category: station.category,
        enterpriseName: station.enterpriseName || '',
        mill: station.millName || '',
        workshop: station.workspaceName || '',
        station: station.stationName || station.name,
        fullPath: station.fullPath || '',
        // 配发标准相关 - 直接返回 products 数组作为 protectionPlans
        protectionPlans: protectionPlan && protectionPlan.products ? protectionPlan.products : [],
        configStatus: protectionPlan ?
          (protectionPlan.configStatus === 'no_need' ? 'no_need' :
            (protectionPlan.products && protectionPlan.products.length > 0 ? 'configured' : 'unconfigured')) :
          'unconfigured',
        // 如果有配发标准，返回其ID用于更新
        existingPlanId: protectionPlan ? protectionPlan._id : null,
      };

      ctx.body = { code: 200, status: 200, data: result };
    } catch (error) {
      ctx.logger.error('获取工种详情失败:', error);
      ctx.body = { code: 500, status: 500, message: '获取工种详情失败' };
    }
  },
  async createWord(ctx, app) {
    // 下载个人防护用品记录清单word
    const data = ctx.request.body.data;
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item && item.sign) {
        const sign = item.sign.split('/');
        item.sign = path.join(
          app.config.image_upload_path,
          sign[sign.length - 2],
          sign[sign.length - 1]
        );
      }
    }
    const fileNameSuffix = '';
    const templateFileName = '个人防护用品领用清单';
    // const data2 = data.
    const res = await ctx.helper.fillWord(
      ctx,
      templateFileName,
      JSON.parse(JSON.stringify({ data })),
      fileNameSuffix
    );
    ctx.body = { code: 200, status: 200, data: res };
  },
  async replenishment(ctx) {
    const replenishment = ctx.query.replenishment;
    const EnterpriseId = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    const ppeSelfLiftCabinet = await ctx.service.db.findOne(
      'PpeSelfLiftCabinet',
      { EnterpriseId }
    );
    if (ppeSelfLiftCabinet && !replenishment) {
      ctx.helper.renderSuccess(ctx, {
        data: true,
        message: '此公司有ppe自提柜',
      });
      return;
    } else if (replenishment && ppeSelfLiftCabinet) {
      for (let i = 0; i < ppeSelfLiftCabinet.specifications.length; i++) {
        ppeSelfLiftCabinet.specifications[i].surplus =
          ppeSelfLiftCabinet.specifications[i].totality;
      }
      await ctx.service.db.updateOne(
        'PpeSelfLiftCabinet',
        { EnterpriseId },
        { $set: { specifications: ppeSelfLiftCabinet.specifications } }
      );
    } else if (!ppeSelfLiftCabinet) {
      ctx.helper.renderSuccess(ctx, {
        data: false,
        message: '此公司无ppe自提柜',
      });
    }
  },
  async delCategory(ctx) {
    const _id = ctx.query._id;
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      await ctx.service.db.updateOne(
        'ProtectiveSuppliesList',
        { EnterpriseID },
        { $pull: { list: { _id } } }
      );
      ctx.helper.renderSuccess(ctx, {
        message: '删除成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  async immediatePPEPlan(ctx) {
    try {
      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      await ctx.service.defendproducts.immediatePPEPlan(EnterpriseID);
      ctx.helper.renderSuccess(ctx, {
        message: '删除成功',
      });
    } catch (err) {
      console.log(err, '立即生成PPE计划错误');
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // ==================== 新增：防护用品产品管理API ====================

  /**
   * 获取分类的导入模板
   * GET /api/defendproducts/getImportTemplate?categoryId=xxx&warehouseId=xxx
   */
  async getImportTemplate(ctx) {
    try {
      const { categoryId, warehouseId } = ctx.query;

      if (!categoryId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少分类ID参数',
        });
        return;
      }

      const templateData = await ctx.service.protectiveProductImport.generateImportTemplate(categoryId, warehouseId);

      ctx.helper.renderSuccess(ctx, {
        data: templateData,
        message: '获取导入模板成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取导入模板失败',
      });
    }
  },

  /**
   * 下载Excel导入模板
   * GET /api/defendproducts/downloadTemplate?categoryId=xxx&warehouseId=xxx&templateType=xxx
   */
  async downloadTemplate(ctx) {
    try {
      const { categoryId, warehouseId = 'public', templateType } = ctx.query;

      let templateData;
      let fileName;

      if (templateType === 'universal') {
        // 生成通用模板
        templateData = await ctx.service.protectiveProductImport.generateUniversalTemplate(warehouseId);
        fileName = '防护用品通用导入模板.xlsx';
      } else {
        // 生成分类专用模板
        if (!categoryId) {
          ctx.helper.renderFail(ctx, {
            message: '缺少分类ID参数',
          });
          return;
        }
        templateData = await ctx.service.protectiveProductImport.generateImportTemplate(categoryId, warehouseId);
        fileName = `${templateData.categoryName || '分类'}_导入模板.xlsx`;
      }

      // 生成Excel文件
      const excelBuffer = await ctx.service.protectiveProductImport.generateExcelFile(templateData);

      // 设置响应头 - 修复文件名undefined问题
      // 优先使用预定义的fileName，如果没有则使用templateData.sheetName
      const downloadFileName = fileName || templateData.sheetName || '导入模板.xlsx';

      ctx.set({
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename=${encodeURIComponent(downloadFileName)}`,
      });

      ctx.body = excelBuffer;
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '下载模板失败',
      });
    }
  },

  /**
   * 解析导入文件
   * POST /api/defendproducts/parseImportFile
   */
  async parseImportFile(ctx) {
    try {
      // 修复参数获取方式：使用multipart解析FormData
      const parts = ctx.multipart({ autoFields: true });
      let uploadedFile = null;

      // 获取文件
      let stream;
      while ((stream = await parts()) != null) {
        if (stream.filename) {
          uploadedFile = stream;
          break;
        }
      }

      // 从parts.field中获取FormData参数
      const { categoryId, warehouseId, intelligentParsing } = parts.field || {};

      // 调试信息
      console.log('parseImportFile 接收到的参数:', {
        categoryId,
        warehouseId,
        intelligentParsing,
        intelligentParsingType: typeof intelligentParsing,
        hasFile: !!uploadedFile
      });

      // 将字符串转换为布尔值 - 修复参数验证问题
      const isIntelligentParsing = intelligentParsing === 'true' || intelligentParsing === true;

      console.log('转换后的智能解析标志:', isIntelligentParsing);

      // 修复验证逻辑：智能解析模式下不需要categoryId，传统模式下需要categoryId
      if (!isIntelligentParsing && (!categoryId || categoryId.trim() === '')) {
        console.log('参数验证失败，传统模式下缺少分类ID');
        ctx.helper.renderFail(ctx, {
          message: '传统导入模式需要指定分类ID参数',
        });
        return;
      }

      if (!uploadedFile) {
        ctx.helper.renderFail(ctx, {
          message: '请选择要上传的文件',
        });
        return;
      }

      // 保存临时文件
      const fs = require('fs');
      const path = require('path');
      const tempDir = path.join(__dirname, '../../../temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFilePath = path.join(tempDir, `upload_${Date.now()}_${uploadedFile.filename}`);
      const writeStream = fs.createWriteStream(tempFilePath);
      uploadedFile.pipe(writeStream);

      await new Promise((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
      });

      // 解析文件
      let result;
      if (isIntelligentParsing) {
        // 智能解析模式
        result = await ctx.service.protectiveProductImport.parseImportFileIntelligent(tempFilePath, warehouseId);
      } else {
        // 传统解析模式
        result = await ctx.service.protectiveProductImport.parseImportFile(tempFilePath, categoryId);
      }

      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '文件解析成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '文件解析失败',
      });
    }
  },

  /**
   * 导入防护用品产品
   * POST /api/defendproducts/importProducts
   */
  async importProducts(ctx) {
    try {
      const { data, categoryId, warehouseId, options = {}, intelligentImport } = ctx.request.body;

      if (!data || !Array.isArray(data) || data.length === 0) {
        ctx.helper.renderFail(ctx, {
          message: '缺少导入数据',
        });
        return;
      }

      if (!intelligentImport && !categoryId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少分类ID参数',
        });
        return;
      }

      let result;
      if (intelligentImport) {
        // 智能导入模式
        result = await ctx.service.protectiveProductImport.importProductsIntelligent(data, warehouseId, options);
      } else {
        // 传统导入模式
        result = await ctx.service.protectiveProductImport.importProducts(data, categoryId, warehouseId, options);
      }

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '产品导入成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '产品导入失败',
      });
    }
  },

  /**
   * 获取防护用品产品列表
   * POST /api/defendproducts/getProductList
   */
  async getProductList(ctx) {
    try {
      const params = ctx.request.body;
      const result = await ctx.service.protectiveProductImport.getProductList(params);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取产品列表成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取产品列表失败',
      });
    }
  },

  /**
   * 保存防护用品产品
   * POST /api/defendproducts/saveProduct
   */
  async saveProduct(ctx) {
    try {
      const productData = ctx.request.body;
      const result = await ctx.service.protectiveProductImport.saveProduct(productData);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '保存产品成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '保存产品失败',
      });
    }
  },

  /**
   * 删除防护用品产品
   * POST /api/defendproducts/deleteProducts
   */
  async deleteProducts(ctx) {
    try {
      const { ids } = ctx.request.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        ctx.helper.renderFail(ctx, {
          message: '缺少产品ID参数',
        });
        return;
      }

      const result = await ctx.service.protectiveProductImport.deleteProducts(ids);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '删除产品成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '删除产品失败',
      });
    }
  },

  // ==================== 模板管理API ====================

  /**
   * 获取分类的模板列表
   * GET /api/defendproducts/getTemplatesByCategory?categoryId=xxx
   */
  async getTemplatesByCategory(ctx) {
    try {
      const { categoryId } = ctx.query;

      if (!categoryId) {
        ctx.helper.renderFail(ctx, {
          message: '缺少分类ID参数',
        });
        return;
      }

      const EnterpriseID = ctx.session.adminUserInfo?.EnterpriseID;
      const templates = await ctx.service.db.find('ProtectiveProductTemplate', {
        EnterpriseID,
        categoryId,
        isActive: true,
      });

      ctx.helper.renderSuccess(ctx, {
        data: templates,
        message: '获取模板列表成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取模板列表失败',
      });
    }
  },

  /**
   * 保存模板配置
   * POST /api/defendproducts/saveTemplate
   */
  async saveTemplate(ctx) {
    try {
      const templateData = ctx.request.body;
      const EnterpriseID = ctx.session.adminUserInfo?.EnterpriseID;

      templateData.EnterpriseID = EnterpriseID;

      let result;
      if (templateData._id) {
        // 更新模板
        result = await ctx.service.db.updateOne('ProtectiveProductTemplate',
          { _id: templateData._id },
          templateData
        );
      } else {
        // 创建新模板
        result = await ctx.service.db.create('ProtectiveProductTemplate', templateData);
      }

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '保存模板成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '保存模板失败',
      });
    }
  },

  async delProtectionPlan(ctx) {
    const { ids } = ctx.request.body;

    // 删除发放标准，同时删除未领取的领用记录
    await ctx.service.db.remove('ProtectionPlan', { _id: { $in: ids } });

    // 删除未领取的领用记录
    await ctx.service.db.remove('ReceiveRecord', {
      planId: { $in: ids },
      sign: { $exists: false },
    });

    ctx.helper.renderSuccess(ctx, {
      message: '删除成功',
    });
  },

  async addProtectionPlan(ctx) {
    const { data, type, grantType } = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';

    // 批量导入
    if (type === 'import') {
      const { noExistProducts } =
        await ctx.service.defendproducts.handleImportPlans(data, grantType);
      ctx.helper.renderSuccess(ctx, {
        data: {
          noExistProducts,
        },
        message: '计划导入成功',
      });
      return;
    }

    // 手动新建
    // 先判断是否存在发放计划
    const item = data;
    const query = {};

    if (item.grantType === 'mill') {
      // 优先使用新的 nodeFullId 格式
      if (item.nodeFullId) {
        query.nodeFullId = item.nodeFullId;
      } else {
        // 兼容旧格式
        if (item.workshop) {
          query.workshop = item.workshop;
        }
        if (item.workspaces) {
          query.workspaces = item.workspaces;
        }
        if (item.workstation) {
          query.workstation = item.workstation;
        }
      }
    } else if (item.grantType === 'depart') {
      query.departId = item.departId;
    }

    let res = await ctx.service.db.findOne('ProtectionPlan', query);

    if (!res) {
      const data = {
        EnterpriseID: item.enterprises || EnterpriseID,
        grantType: item.grantType,
        products: item.formData.products,
        category: item.formData.category || '',
      };

      // 处理新的 fullId 格式
      if (item.nodeFullId) {
        data.nodeFullId = item.nodeFullId;
        data.nodeLevel = item.nodeLevel;
        data.nodeName = item.nodeName;
      } else {
        // 兼容旧格式
        data.workshop = item.workshop ? item.workshop : '';
        data.workspaces = item.workspaces ? item.workspaces : '';
        data.workspacesName = item.workspacesName ? item.workspacesName : '';
        data.workstation = item.workstation ? item.workstation : '';
        data.workstationName = item.workstationName ? item.workstationName : '';
      }

      // 部门相关字段
      if (item.grantType === 'depart') {
        data.departId = item.departId ? item.departId : '';
        data.departName = item.departName ? item.departName : '';
      }
      data.subRegion = item.selectChildMills;
      await ctx.service.db.create('ProtectionPlan', data);
      res = await ctx.service.db.findOne('ProtectionPlan', query);
    } else {
      // 移除之前未领用的记录
      await ctx.service.db.remove('ReceiveRecord', {
        planId: res._id,
        sign: { $exists: false },
      });
      await ctx.service.db.updateOne(
        'ProtectionPlan',
        { _id: res._id },
        {
          $set: {
            products: item.formData.products,
            subRegion: item.selectChildMills,
            category: item.formData.category || '',
          },
        }
      );
    }

    // 生成领用记录
    // const formData = item.formData;
    // const startDate = moment(formData.startDate || new Date());
    // const warningDate = moment(formData.startDate).add(5, 'd');
    // const plan = {
    //   workshop: item.workshop,
    //   workshopName: item.workshopName,
    //   workspaces: item.workspaces,
    //   workspacesName: item.workspacesName,
    //   workstation: item.workstation,
    //   workstationName: item.workstationName,
    //   departId: item.departId,
    //   employee: item.employee,
    //   _id: shortid.generate(),
    //   timeUnit: formData.timeUnit,
    //   time: formData.time,
    //   startDate,
    //   planStatus: 1,
    //   planId: res._id,
    // };

    // plan.receiveStartDate = startDate;
    // plan.EnterpriseID = item.enterprises || EnterpriseID;
    // plan.warningDate = warningDate;
    // plan.planId = res._id;

    // const completeRes = await ctx.service.receiveRecord.createReceiveRecordByPlan({ plan, hasCompleteEmployee, products: formData.products, grantType: item.grantType });
    // hasCompleteEmployee.push(...completeRes);

    ctx.helper.renderSuccess(ctx, {
      data: {},
      message: '计划新建成功',
    });
  },

  async getReceiveRecordList(ctx) {
    const body = ctx.request.body;
    const params = {
      startTime: body.startTime,
      endTime: body.endTime,
      employee: body.employee, // 支持按员工ID查询
      keyword: body.nameOrEvent, // 支持关键字搜索
      receiveStartDate: body.receiveStartDate,
    };

    const pagination = {
      current: body.current || 1,
      pageSize: body.pageSize || 10,
    };

    const res = await ctx.service.receiveRecord.getList({
      params,
      pagination,
    });

    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '获取成功',
    });
  },
  async delReceiveRecord(ctx) {
    const { ids } = ctx.request.body;
    await ctx.service.receiveRecord.remove({ _id: { $in: ids } });
    ctx.helper.renderSuccess(ctx, {
      message: '删除成功',
    });
  },

  // 获取申请列表
  async getApplicationProducts(ctx) {
    try {
      console.log('进来了~2232');
      const params = ctx.request.body;
      console.log(params, 'params');
      const res = await ctx.service.applicationProduct.getList(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      ctx.auditLog('获取申请列表失败:' + err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 改变申请状态
  async selectApplication(ctx) {
    try {
      console.log('进来了~selectApplication');
      const params = ctx.request.body;
      const res = await ctx.service.applicationProduct.selectApplication(
        params
      );
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      ctx.auditLog('改变申请状态失败:' + err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 导出领用记录
  async exportReceiveRecords(ctx) {
    try {
      console.log('进来了~exportReceiveRecords');
      const params = ctx.request.body;
      const res = await ctx.service.receiveRecord.exportReceiveRecords(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      ctx.auditLog('导出领用记录失败:' + err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  /**
   * @summary 获取扁平化防护用品标准列表
   * @description 获取以工种为行的防护用品标准列表，支持筛选和分页
   * @return {Promise<void>} 无返回值
   */
  async getFlatStandardsList(ctx) {
    try {
      // 1. 参数获取和校验
      const { filters = {}, page = 1, limit = 20 } = ctx.request.body;

      // 2. 业务处理（调用 service）
      const result =
        await ctx.service.protectionStandardsFlat.getFlatStandardsList(
          filters,
          { page: parseInt(page), limit: parseInt(limit) }
        );

      // 3. 响应处理
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取扁平化防护用品标准列表成功',
      });
    } catch (error) {
      // 4. 错误处理
      ctx.logger.error('获取扁平化防护用品标准列表失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取扁平化防护用品标准列表失败',
      });
    }
  },

  /**
   * @summary 获取筛选选项数据
   * @description 获取用于筛选的各种选项数据
   * @return {Promise<void>} 无返回值
   */
  async getFilterOptions(ctx) {
    try {
      // 1. 业务处理（调用 service）
      const result = await ctx.service.protectionStandardsFlat.getFilterOptions();

      // 2. 响应处理
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取筛选选项数据成功',
      });
    } catch (error) {
      // 3. 错误处理
      ctx.logger.error('获取筛选选项数据失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取筛选选项数据失败',
      });
    }
  },

  /**
   * @summary 批量更新配置状态
   * @description 批量更新工种的配置状态
   * @return {Promise<void>} 无返回值
   */
  async updateConfigStatus(ctx) {
    try {
      // 1. 参数获取和校验
      const { workStationIds, configStatus } = ctx.request.body;

      if (!workStationIds || !Array.isArray(workStationIds) || workStationIds.length === 0) {
        ctx.helper.renderFail(ctx, {
          message: '工种ID列表不能为空',
        }, 400);
        return;
      }

      if (!configStatus) {
        ctx.helper.renderFail(ctx, {
          message: '配置状态不能为空',
        }, 400);
        return;
      }

      // 2. 业务处理（调用 service）
      await ctx.service.protectionStandardsFlat.updateConfigStatus(workStationIds, configStatus);

      // 3. 响应处理
      ctx.helper.renderSuccess(ctx, {
        message: '批量更新配置状态成功',
      });
    } catch (error) {
      // 4. 错误处理
      ctx.logger.error('批量更新配置状态失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '批量更新配置状态失败',
      });
    }
  },

  /**
   * @summary 获取筛选选项数据
   * @description 获取用于筛选的选项数据，包括公司、事业部、部门等
   * @return {Promise<void>} 无返回值
   */
  async getFilterOptions(ctx) {
    try {
      // 1. 业务处理（调用 service）
      const result =
        await ctx.service.protectionStandardsFlat.getFilterOptions();

      // 2. 响应处理
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取筛选选项成功',
      });
    } catch (error) {
      // 3. 错误处理
      ctx.logger.error('获取筛选选项失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取筛选选项失败',
      });
    }
  },
  /**
   * @summary 批量更新配置状态
   * @description 批量更新工种的配置状态
   * @return {Promise<void>} 无返回值
   */
  async updateConfigStatus(ctx) {
    try {
      // 1. 参数获取和校验
      const { workStationIds, configStatus } = ctx.request.body;

      if (
        !workStationIds ||
        !Array.isArray(workStationIds) ||
        workStationIds.length === 0
      ) {
        ctx.helper.renderFail(
          ctx,
          {
            message: '工种ID列表不能为空',
          },
          400
        );
        return;
      }

      if (
        !configStatus ||
        ![ 'configured', 'unconfigured', 'no_need' ].includes(configStatus)
      ) {
        ctx.helper.renderFail(
          ctx,
          {
            message: '配置状态参数无效',
          },
          400
        );
        return;
      }

      // 2. 业务处理（调用 service）
      const result =
        await ctx.service.protectionStandardsFlat.updateConfigStatus(
          workStationIds,
          configStatus
        );

      // 3. 响应处理
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '配置状态更新成功',
      });
    } catch (error) {
      // 4. 错误处理
      ctx.logger.error('更新配置状态失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '更新配置状态失败',
      });
    }
  },
  /**
   * @summary 获取工种详细信息
   * @description 获取工种的详细信息，用于配置弹窗显示
   * @return {Promise<void>} 无返回值
   */
  async getWorkStationDetail(ctx) {
    try {
      // 1. 参数获取和校验
      const { workStationId } = ctx.request.body;

      if (!workStationId) {
        ctx.helper.renderFail(
          ctx,
          {
            message: '工种ID不能为空',
          },
          400
        );
        return;
      }

      const EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';

      // 2. 业务处理
      // 获取工种基本信息
      const workStation =
        await ctx.model.FlatMillConstructionMaterialized.findOne({
          _id: workStationId,
          level: 'stations',
        });

      if (!workStation) {
        ctx.helper.renderFail(
          ctx,
          {
            message: '工种不存在',
          },
          404
        );
        return;
      }

      // 获取该工种的防护用品配置
      const protectionPlans = await ctx.model.protectionPlan.find({
        EnterpriseID,
        workstation: workStation.fullId,
      });

      // 获取该工种的员工信息
      const employees = await ctx.model.Employee.find({
        workstation: workStation.fullId,
      });

      // 3. 响应处理
      ctx.helper.renderSuccess(ctx, {
        data: {
          workStation,
          protectionPlans,
          employees,
        },
        message: '获取工种详细信息成功',
      });
    } catch (error) {
      // 4. 错误处理
      ctx.logger.error('获取工种详细信息失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取工种详细信息失败',
      });
    }
  },

  // ==================== 防护用品分类管理 API ====================

  /**
   * 获取分类树
   * GET /api/defendproducts/getProtectionCategoryTree
   */
  async getProtectionCategoryTree(ctx) {
    try {
      const { enterpriseId, includeSystem, level, activeOnly } = ctx.query;
      const options = {
        enterpriseId,
        includeSystem: includeSystem === 'true',
        level: level ? parseInt(level) : undefined,
        activeOnly: activeOnly !== 'false',
      };

      const tree = await ctx.service.protectionCategory.buildCategoryTree(options);
      ctx.helper.renderSuccess(ctx, {
        data: tree,
        message: '获取分类树成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取分类树失败',
      });
    }
  },

  /**
   * 获取叶子节点列表
   */
  async getProtectionCategoryLeaves(ctx) {
    try {
      const options = ctx.request.body;
      const leaves = await ctx.service.protectionCategory.getLeafCategories(options);
      ctx.helper.renderSuccess(ctx, {
        data: leaves,
        message: '获取叶子节点成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取叶子节点失败',
      });
    }
  },

  /**
   * 创建分类
   */
  async createProtectionCategory(ctx) {
    try {
      const categoryData = ctx.request.body;
      const result = await ctx.service.protectionCategory.createCategory(categoryData);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建分类成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '创建分类失败',
      });
    }
  },

  /**
   * 更新分类
   */
  async updateProtectionCategory(ctx) {
    try {
      const { id, ...updateData } = ctx.request.body;
      if (!id) {
        ctx.helper.renderFail(ctx, {
          message: '分类ID不能为空',
        });
        return;
      }

      const result = await ctx.service.protectionCategory.updateCategory(id, updateData);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '更新分类成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '更新分类失败',
      });
    }
  },

  /**
   * 删除分类
   */
  async deleteProtectionCategory(ctx) {
    try {
      const { id } = ctx.request.body;
      if (!id) {
        ctx.helper.renderFail(ctx, {
          message: '分类ID不能为空',
        });
        return;
      }

      const result = await ctx.service.protectionCategory.deleteCategory(id);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '删除分类成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '删除分类失败',
      });
    }
  },

  /**
   * 批量操作分类
   */
  async batchProtectionCategory(ctx) {
    try {
      const { action, ids, data } = ctx.request.body;
      if (!action || !ids || !Array.isArray(ids)) {
        ctx.helper.renderFail(ctx, {
          message: '参数不正确',
        });
        return;
      }

      const result = await ctx.service.protectionCategory.batchOperation(action, ids, data);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '批量操作成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '批量操作失败',
      });
    }
  },

  /**
   * 智能推荐分类
   */
  async recommendProtectionCategory(ctx) {
    try {
      const { harmFactors, workstation } = ctx.request.body;
      if (!harmFactors || !Array.isArray(harmFactors)) {
        ctx.helper.renderFail(ctx, {
          message: '危害因素不能为空',
        });
        return;
      }

      const context = workstation ? { workstation } : {};
      const recommendations = await ctx.service.protectionCategory.recommendCategories(harmFactors, context);
      ctx.helper.renderSuccess(ctx, {
        data: recommendations,
        message: '智能推荐成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '智能推荐失败',
      });
    }
  },

  /**
   * 检查分类使用情况
   */
  async checkProtectionCategoryUsage(ctx) {
    try {
      const { categoryIds } = ctx.request.body;
      if (!categoryIds) {
        ctx.helper.renderFail(ctx, {
          message: '分类ID不能为空',
        });
        return;
      }

      const usage = await ctx.service.protectionCategory.checkCategoryUsage(categoryIds);
      ctx.helper.renderSuccess(ctx, {
        data: usage,
        message: '检查使用情况成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '检查使用情况失败',
      });
    }
  },

  /**
   * 查找危害因素
   */
  async findHarmFactors(ctx) {
    try {
      const { query } = ctx.request.body;
      // 调用现有的危害因素查询服务
      const queryParams = typeof query === 'string' ? JSON.parse(query) : query;
      const result = await ctx.service.jobHealth.findHarmFactors(queryParams);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '查找危害因素成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '查找危害因素失败',
      });
    }
  },

  /**
   * 导入系统默认分类数据
   */
  async importSystemCategories(ctx) {
    try {
      // 导入脚本
      const importScript = require(ctx.app.baseDir + '/app/script/importProtectionCategoryData.js');

      // 执行导入
      const result = await importScript.importCategoryData(ctx);

      if (result.success) {
        ctx.helper.renderSuccess(ctx, {
          data: result.statistics,
          message: '系统默认分类数据导入成功',
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: result.message || '导入失败',
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '导入系统默认分类数据失败',
      });
    }
  },

  /**
   * 清理系统默认分类数据
   */
  async cleanSystemCategories(ctx) {
    try {
      // 导入脚本
      const importScript = require(ctx.app.baseDir + '/app/script/importProtectionCategoryData.js');

      // 执行清理
      const result = await importScript.cleanSystemCategories(ctx);

      if (result.success) {
        ctx.helper.renderSuccess(ctx, {
          data: { deletedCount: result.deletedCount },
          message: '系统默认分类数据清理成功',
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: result.message || '清理失败',
        });
      }
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '清理系统默认分类数据失败',
      });
    }
  },

  // ==================== Excel导入功能 API ====================

  /**
   * 下载防护用品分类导入模板
   * GET /api/defendproducts/downloadCategoryTemplate
   */
  async downloadCategoryTemplate(ctx) {
    try {
      const path = require('path');
      const fs = require('fs');

      const templatePath = path.join(ctx.app.baseDir, 'app/public/dataTemplate/防护用品分类导入模板.xlsx');

      // 检查文件是否存在
      if (!fs.existsSync(templatePath)) {
        ctx.helper.renderFail(ctx, {
          message: '模板文件不存在，请联系管理员',
        });
        return;
      }

      // 设置响应头
      ctx.set({
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename=protection-category-template.xlsx',
      });

      // 返回文件流
      ctx.body = fs.createReadStream(templatePath);
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '下载模板失败',
      });
    }
  },

  /**
   * 上传并解析Excel文件
   * POST /api/defendproducts/uploadCategoryFile
   */
  async uploadCategoryFile(ctx) {
    const sendToWormhole = require('stream-wormhole');

    try {
      const parts = ctx.multipart({ autoFields: true });
      let stream;
      let uploadedFile = null;

      // 处理上传的文件
      while ((stream = await parts()) != null) {
        if (!stream.filename) {
          continue;
        }

        // 验证文件类型
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
        ];

        if (!allowedTypes.includes(stream.mimeType)) {
          await sendToWormhole(stream);
          ctx.helper.renderFail(ctx, {
            message: '只支持Excel文件格式(.xlsx, .xls)',
          });
          return;
        }

        // 保存文件到临时目录
        const path = require('path');
        const fs = require('fs');
        const { mkdirp } = require('mkdirp');
        const tempDir = path.join(ctx.app.baseDir, 'temp');

        // 确保临时目录存在
        await mkdirp(tempDir);

        const tempFileName = `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}.xlsx`;
        const tempFilePath = path.join(tempDir, tempFileName);

        try {
          // 使用fs.createWriteStream保存文件
          const writeStream = fs.createWriteStream(tempFilePath);

          // 将上传流写入文件
          await new Promise((resolve, reject) => {
            stream.pipe(writeStream);

            writeStream.on('finish', () => {
              resolve();
            });

            writeStream.on('error', (error) => {
              reject(error);
            });

            stream.on('error', (error) => {
              writeStream.destroy();
              reject(error);
            });
          });

          // 验证文件是否保存成功
          if (!fs.existsSync(tempFilePath)) {
            throw new Error('文件保存失败');
          }

          uploadedFile = {
            filename: stream.filename,
            filepath: tempFilePath,
            mimeType: stream.mimeType,
          };
        } catch (error) {
          ctx.logger.error('文件保存失败:', error);
          await sendToWormhole(stream);
          ctx.helper.renderFail(ctx, {
            message: '文件保存失败: ' + error.message,
          });
          return;
        }

        break; // 只处理第一个文件
      }

      if (!uploadedFile) {
        ctx.helper.renderFail(ctx, {
          message: '请选择要上传的文件',
        });
        return;
      }

      // 解析Excel文件
      const result = await ctx.service.protectionCategoryImport.parseExcelFile(uploadedFile.filepath);

      // 清理临时文件
      const fs = require('fs');
      if (fs.existsSync(uploadedFile.filepath)) {
        fs.unlinkSync(uploadedFile.filepath);
      }

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '文件解析成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '文件上传解析失败',
      });
    }
  },

  /**
   * 预览导入数据
   * POST /api/defendproducts/previewCategoryData
   */
  async previewCategoryData(ctx) {
    try {
      const { excelData, options = {} } = ctx.request.body;

      if (!excelData) {
        ctx.helper.renderFail(ctx, {
          message: '缺少Excel数据',
        });
        return;
      }

      // 数据验证和预览
      const result = await ctx.service.protectionCategoryImport.previewData(excelData, options);

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '数据预览成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '数据预览失败',
      });
    }
  },

  /**
   * 开始导入数据
   * POST /api/defendproducts/importCategoryData
   */
  async importCategoryData(ctx) {
    try {
      const { excelData, options = {} } = ctx.request.body;

      if (!excelData) {
        ctx.helper.renderFail(ctx, {
          message: '缺少Excel数据',
        });
        return;
      }

      // 生成任务ID
      const taskId = `import_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 初始化EventEmitter（确保在导入服务执行前就存在）
      if (!ctx.app.progressEmitter) {
        const EventEmitter = require('events');
        ctx.app.progressEmitter = new EventEmitter();
        console.log('🔧 EventEmitter 初始化完成');
      }

      // 异步执行导入
      console.log('🚀 开始异步执行导入，任务ID:', taskId);
      console.log('📊 Excel数据统计:', {
        basicData: (excelData.basicData && excelData.basicData.length) || 0,
        attributeData: (excelData.attributeData && excelData.attributeData.length) || 0,
        harmFactorsData: (excelData.harmFactorsData && excelData.harmFactorsData.length) || 0
      });

      ctx.service.protectionCategoryImport.importData(taskId, excelData, options)
        .catch(error => {
          console.error('❌ 导入数据异步处理失败:', error);
          ctx.logger.error('导入数据异步处理失败:', error);
        });

      ctx.helper.renderSuccess(ctx, {
        data: { taskId },
        message: '导入任务已启动',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message || '启动导入任务失败',
      });
    }
  },

  /**
   * SSE进度推送
   * GET /api/defendproducts/importProgress?taskId=xxx
   */
  async importProgress(ctx) {
    // 直接使用原生 Node.js 响应，绕过 Egg.js
    const res = ctx.res;
    const { taskId } = ctx.query;

    try {
      console.log('🚀 SSE 连接已建立，任务ID:', taskId);

      // 设置 SSE 响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // 阻止 Egg.js 自动处理响应
      ctx.respond = false;

      // 禁用请求超时
      ctx.req.setTimeout(0);
      res.setTimeout(0);

      // 发送连接成功消息
      console.log('📋 发送连接消息...');
      res.write(`data: ${JSON.stringify({
        type: 'connected',
        message: 'SSE连接已建立',
        taskId,
      })}\n\n`);
      console.log('✅ 连接消息发送成功');

      // 设置心跳保持连接
      const heartbeat = setInterval(() => {
        if (!res.finished && !res.destroyed) {
          try {
            res.write(`data: ${JSON.stringify({
              type: 'heartbeat',
              timestamp: Date.now(),
            })}\n\n`);
          } catch (error) {
            console.error('心跳发送失败:', error);
            clearInterval(heartbeat);
          }
        } else {
          clearInterval(heartbeat);
        }
      }, 5000); // 每5秒发送一次心跳

      // 监听真实的导入进度
      const progressListener = (progress) => {
        if (res.finished || res.destroyed) {
          return;
        }

        try {
          res.write(`data: ${JSON.stringify({
            type: 'progress',
            ...progress,
          })}\n\n`);
        } catch (error) {
          console.error('进度发送失败:', error);
        }
      };

      const completeListener = (result) => {
        if (res.finished || res.destroyed) {
          return;
        }

        try {
          res.write(`data: ${JSON.stringify({
            type: 'completed',
            ...result,
          })}\n\n`);

          clearInterval(heartbeat);
          setTimeout(() => {
            if (!res.finished && !res.destroyed) {
              res.end();
            }
          }, 1000);
        } catch (error) {
          console.error('完成消息发送失败:', error);
        }
      };

      const errorListener = (error) => {
        if (res.finished || res.destroyed) {
          return;
        }

        try {
          res.write(`data: ${JSON.stringify({
            type: 'error',
            message: error.message,
          })}\n\n`);

          clearInterval(heartbeat);
          setTimeout(() => {
            if (!res.finished && !res.destroyed) {
              res.end();
            }
          }, 1000);
        } catch (err) {
          console.error('错误消息发送失败:', err);
        }
      };

      // 初始化EventEmitter
      if (!ctx.app.progressEmitter) {
        const EventEmitter = require('events');
        ctx.app.progressEmitter = new EventEmitter();
      }

      // 注册监听器
      ctx.app.progressEmitter.on(`progress_${taskId}`, progressListener);
      ctx.app.progressEmitter.on(`complete_${taskId}`, completeListener);
      ctx.app.progressEmitter.on(`error_${taskId}`, errorListener);

      // 清理函数
      const cleanup = () => {
        console.log('🧹 清理SSE监听器');
        clearInterval(heartbeat);
        ctx.app.progressEmitter.off(`progress_${taskId}`, progressListener);
        ctx.app.progressEmitter.off(`complete_${taskId}`, completeListener);
        ctx.app.progressEmitter.off(`error_${taskId}`, errorListener);
      };

      // 客户端断开连接时清理
      ctx.req.on('close', () => {
        console.log('🔚 客户端断开连接');
        cleanup();
      });

      ctx.req.on('error', () => {
        console.log('❌ 连接错误');
        cleanup();
      });

    } catch (error) {
      console.error('❌ SSE 处理出错:', error);

      // 发送错误消息
      if (!res.finished && !res.destroyed) {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          message: error.message || '处理过程中发生错误',
        })}\n\n`);
        res.end();
      } else {
        console.error('⚠️  无法发送错误消息，响应已结束');
      }
    }
  },

  /**
   * @summary 获取岗位汇总领用状态
   * @description 按岗位汇总查看防护用品领用情况
   */
  async getStationSummaryReceiveStatus(ctx) {
    try {
      const { year, month, workUnitId, configStatus } = ctx.request.body;

      // 参数验证
      if (!year || !month || !workUnitId) {
        ctx.helper.renderFail(ctx, {
          message: '年份、月份和工作单元ID不能为空'
        }, 400);
        return;
      }

      const result = await ctx.service.receiveRecord.getStationSummaryReceiveStatus({
        year: parseInt(year),
        month: parseInt(month),
        workUnitId,
        configStatus // 传递配发标准状态筛选参数
      });

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取岗位汇总领用状态成功'
      });
    } catch (error) {
      ctx.logger.error('获取岗位汇总领用状态失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取岗位汇总领用状态失败'
      });
    }
  },

  /**
   * @summary 获取岗位详细员工领用状态
   * @description 查看指定岗位下所有员工的详细领用情况
   */
  async getStationDetailReceiveStatus(ctx) {
    try {
      const { stationId, year, month } = ctx.request.body;

      // 参数验证
      if (!stationId || !year || !month) {
        ctx.helper.renderFail(ctx, {
          message: '岗位ID、年份和月份不能为空'
        }, 400);
        return;
      }

      const result = await ctx.service.receiveRecord.getStationDetailReceiveStatus({
        stationId,
        year: parseInt(year),
        month: parseInt(month)
      });

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取岗位详细员工领用状态成功'
      });
    } catch (error) {
      ctx.logger.error('获取岗位详细员工领用状态失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取岗位详细员工领用状态失败'
      });
    }
  },
};

module.exports = DefendProductsApiController;
