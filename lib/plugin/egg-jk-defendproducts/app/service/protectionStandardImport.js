/*
 * @Author: 产品设计
 * @Date: 2024-12-20
 * @Description: 配发标准导入服务
 * 支持基于防护用品分类的配发标准Excel导入功能
 */

const Service = require('egg').Service;
const XLSX = require('xlsx');
const ExcelJS = require('exceljs');

class ProtectionStandardImportService extends Service {

  /**
   * 生成配发标准导入模板
   * @param {String} templateType 模板类型: 'workstation'|'category'|'universal'
   * @param {Object} options 选项参数
   * @returns {Object} Excel模板数据
   */
  async generateStandardTemplate(templateType = 'universal', options = {}) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo?.EnterpriseID;

    try {
      let templateData;

      switch (templateType) {
        case 'workstation':
          templateData = await this.generateWorkstationTemplate(options.workstationId, EnterpriseID);
          break;
        case 'category':
          templateData = await this.generateCategoryTemplate(options.categoryId, EnterpriseID);
          break;
        case 'universal':
        default:
          templateData = await this.generateUniversalTemplate(EnterpriseID, options);
          break;
      }

      return templateData;
    } catch (error) {
      ctx.logger.error('生成配发标准模板失败:', error);
      throw new Error(`生成模板失败: ${error.message}`);
    }
  }

  /**
   * 生成通用配发标准模板
   * @param {String} EnterpriseID 企业ID
   * @param {Object} options 选项
   * @returns {Object} 模板数据
   */
  async generateUniversalTemplate(EnterpriseID, options = {}) {
    const { ctx } = this;

    // 获取企业的组织架构（工种级别）
    const workstations = await ctx.service.protectionStandardsFlat.getFlatStandardsList({
      // 只获取工种级别的数据
    }, { page: 1, limit: 1000 });

    // 获取防护用品分类（叶子节点）
    const categories = await ctx.service.db.find('ProtectionCategory', {
      topEnterpriseId: EnterpriseID,
      isLeaf: true,
      isActive: true,
    });

    // 定义表头结构
    const headers = [
      { key: 'workstationPath', label: '工种路径*', required: true, width: 30 },
      { key: 'workstationName', label: '工种名称*', required: true, width: 20 },
      { key: 'categoryPath', label: '防护用品分类*', required: true, width: 25 },
      { key: 'categoryName', label: '分类名称*', required: true, width: 20 },
      { key: 'number', label: '配发数量*', required: true, width: 12 },
      { key: 'time', label: '周期时间*', required: true, width: 12 },
      { key: 'timeUnit', label: '周期单位*', required: true, width: 12 },
      { key: 'maxNumber', label: '首次最大量', required: false, width: 15 },
      { key: 'remark', label: '备注', required: false, width: 20 },
    ];

    // 生成示例数据
    const sampleData = this.generateStandardSampleData(workstations.data?.list || [], categories);

    return {
      templateType: 'universal',
      sheetName: '配发标准导入模板',
      headers,
      sampleData,
      workstations: workstations.data?.list || [],
      categories,
      validationRules: this.getValidationRules(),
    };
  }

  /**
   * 生成工种专用模板
   * @param {String} workstationId 工种ID
   * @param {String} EnterpriseID 企业ID
   * @returns {Object} 模板数据
   */
  async generateWorkstationTemplate(workstationId, EnterpriseID) {
    const { ctx } = this;

    // 获取工种信息
    const workstation = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
      _id: workstationId,
      category: 'stations',
    });

    if (!workstation) {
      throw new Error('工种不存在');
    }

    // 获取该工种的危害因素
    const harmFactors = workstation.harmFactors || [];

    // 根据危害因素推荐防护用品分类
    const recommendedCategories = await ctx.service.protectionCategory.recommendCategories(harmFactors);

    // 获取所有可用分类（作为备选）
    const allCategories = await ctx.service.db.find('ProtectionCategory', {
      topEnterpriseId: EnterpriseID,
      isLeaf: true,
      isActive: true,
    });

    const headers = [
      { key: 'categoryPath', label: '防护用品分类*', required: true, width: 30 },
      { key: 'categoryName', label: '分类名称*', required: true, width: 20 },
      { key: 'number', label: '配发数量*', required: true, width: 12 },
      { key: 'time', label: '周期时间*', required: true, width: 12 },
      { key: 'timeUnit', label: '周期单位*', required: true, width: 12 },
      { key: 'maxNumber', label: '首次最大量', required: false, width: 15 },
      { key: 'remark', label: '备注', required: false, width: 20 },
    ];

    // 生成推荐的示例数据
    const sampleData = recommendedCategories.map((category, index) => ({
      categoryPath: category.path,
      categoryName: category.name,
      number: category.defaultCycle?.number || 1,
      time: category.defaultCycle?.time || 6,
      timeUnit: category.defaultCycle?.timeUnit || 'M',
      maxNumber: '',
      remark: '系统推荐',
    }));

    return {
      templateType: 'workstation',
      sheetName: `${workstation.name}_配发标准模板`,
      headers,
      sampleData,
      workstation,
      categories: allCategories,
      recommendedCategories,
      validationRules: this.getValidationRules(),
    };
  }

  /**
   * 生成分类专用模板
   * @param {String} categoryId 分类ID
   * @param {String} EnterpriseID 企业ID
   * @returns {Object} 模板数据
   */
  async generateCategoryTemplate(categoryId, EnterpriseID) {
    const { ctx } = this;

    // 获取分类信息
    const category = await ctx.service.db.findOne('ProtectionCategory', {
      _id: categoryId,
      topEnterpriseId: EnterpriseID,
    });

    if (!category) {
      throw new Error('分类不存在');
    }

    // 获取使用该分类的工种
    const existingStandards = await ctx.service.db.find('ProtectionPlan', {
      EnterpriseID,
      'products.categoryId': categoryId,
    });

    // 获取所有工种，排除已配置的
    const allWorkstations = await ctx.service.protectionStandardsFlat.getFlatStandardsList({}, { page: 1, limit: 1000 });
    const configuredWorkstationIds = existingStandards.map(s => s.nodeFullId);
    const availableWorkstations = (allWorkstations.data?.list || []).filter(w => 
      !configuredWorkstationIds.includes(w.fullId)
    );

    const headers = [
      { key: 'workstationPath', label: '工种路径*', required: true, width: 30 },
      { key: 'workstationName', label: '工种名称*', required: true, width: 20 },
      { key: 'number', label: '配发数量*', required: true, width: 12 },
      { key: 'time', label: '周期时间*', required: true, width: 12 },
      { key: 'timeUnit', label: '周期单位*', required: true, width: 12 },
      { key: 'maxNumber', label: '首次最大量', required: false, width: 15 },
      { key: 'remark', label: '备注', required: false, width: 20 },
    ];

    // 生成示例数据
    const sampleData = availableWorkstations.slice(0, 5).map(workstation => ({
      workstationPath: workstation.fullPath,
      workstationName: workstation.name,
      number: category.defaultCycle?.number || 1,
      time: category.defaultCycle?.time || 6,
      timeUnit: category.defaultCycle?.timeUnit || 'M',
      maxNumber: '',
      remark: '',
    }));

    return {
      templateType: 'category',
      sheetName: `${category.name}_配发标准模板`,
      headers,
      sampleData,
      category,
      workstations: availableWorkstations,
      validationRules: this.getValidationRules(),
    };
  }

  /**
   * 生成示例数据
   * @param {Array} workstations 工种列表
   * @param {Array} categories 分类列表
   * @returns {Array} 示例数据
   */
  generateStandardSampleData(workstations, categories) {
    const sampleData = [];
    
    // 取前3个工种和前3个分类生成示例
    const sampleWorkstations = workstations.slice(0, 3);
    const sampleCategories = categories.slice(0, 3);

    sampleWorkstations.forEach((workstation, wIndex) => {
      sampleCategories.forEach((category, cIndex) => {
        sampleData.push({
          workstationPath: workstation.fullPath || `示例车间/示例工种${wIndex + 1}`,
          workstationName: workstation.name || `示例工种${wIndex + 1}`,
          categoryPath: category.path || `示例分类${cIndex + 1}`,
          categoryName: category.name || `示例分类${cIndex + 1}`,
          number: 1,
          time: 6,
          timeUnit: 'M',
          maxNumber: '',
          remark: '示例数据',
        });
      });
    });

    return sampleData;
  }

  /**
   * 获取验证规则
   * @returns {Object} 验证规则
   */
  getValidationRules() {
    return {
      number: { type: 'number', min: 1, max: 999 },
      time: { type: 'number', min: 1, max: 999 },
      timeUnit: { type: 'enum', values: ['d', 'w', 'M', 'Q', 'y'] },
      maxNumber: { type: 'number', min: 0, max: 999, optional: true },
    };
  }

  /**
   * 生成Excel文件
   * @param {Object} templateData 模板数据
   * @returns {Buffer} Excel文件Buffer
   */
  async generateExcelFile(templateData) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(templateData.sheetName || '配发标准导入模板');

    // 设置表头
    const headerRow = worksheet.addRow(templateData.headers.map(h => h.label));
    
    // 设置表头样式
    headerRow.eachCell((cell, colNumber) => {
      const header = templateData.headers[colNumber - 1];
      
      // 必填项红色背景，非必填项蓝色背景
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: header.required ? 'FFDC143C' : 'FF4F81BD' },
      };
      
      cell.font = {
        color: { argb: 'FFFFFFFF' },
        bold: true,
      };
      
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
    });

    // 添加示例数据
    templateData.sampleData.forEach(row => {
      const dataRow = templateData.headers.map(h => row[h.key] || '');
      worksheet.addRow(dataRow);
    });

    // 设置列宽
    templateData.headers.forEach((header, index) => {
      worksheet.getColumn(index + 1).width = header.width || 15;
    });

    // 添加数据验证
    await this.addDataValidation(worksheet, templateData);

    // 生成Excel文件Buffer
    return await workbook.xlsx.writeBuffer();
  }

  /**
   * 添加数据验证
   * @param {Object} worksheet Excel工作表
   * @param {Object} templateData 模板数据
   */
  async addDataValidation(worksheet, templateData) {
    const { headers } = templateData;

    // 为周期单位列添加下拉选择
    const timeUnitColumnIndex = headers.findIndex(h => h.key === 'timeUnit');
    if (timeUnitColumnIndex !== -1) {
      for (let row = 2; row <= 1000; row++) {
        const cell = worksheet.getCell(row, timeUnitColumnIndex + 1);
        cell.dataValidation = {
          type: 'list',
          allowBlank: false,
          formulae: ['"d,w,M,Q,y"'],
        };
      }
    }

    // 为分类路径添加下拉选择（如果有分类数据）
    if (templateData.categories) {
      const categoryPathColumnIndex = headers.findIndex(h => h.key === 'categoryPath');
      if (categoryPathColumnIndex !== -1) {
        const categoryOptions = templateData.categories.map(c => c.path).join(',');
        for (let row = 2; row <= 1000; row++) {
          const cell = worksheet.getCell(row, categoryPathColumnIndex + 1);
          cell.dataValidation = {
            type: 'list',
            allowBlank: false,
            formulae: [`"${categoryOptions}"`],
          };
        }
      }
    }

    // 为工种路径添加下拉选择（如果有工种数据）
    if (templateData.workstations) {
      const workstationPathColumnIndex = headers.findIndex(h => h.key === 'workstationPath');
      if (workstationPathColumnIndex !== -1) {
        const workstationOptions = templateData.workstations.map(w => w.fullPath).join(',');
        for (let row = 2; row <= 1000; row++) {
          const cell = worksheet.getCell(row, workstationPathColumnIndex + 1);
          cell.dataValidation = {
            type: 'list',
            allowBlank: false,
            formulae: [`"${workstationOptions}"`],
          };
        }
      }
    }
  }
}

module.exports = ProtectionStandardImportService;
