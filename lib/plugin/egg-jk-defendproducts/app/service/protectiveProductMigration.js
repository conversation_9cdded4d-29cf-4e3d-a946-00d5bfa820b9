/*
 * @Author: 系统重构
 * @Date: 2024-01-01
 * @Description: 防护用品数据迁移服务
 * 将原有的protectiveSuppliesList嵌套结构迁移到新的protectiveProduct平化结构
 */


const Service = require('egg').Service;

class ProtectiveProductMigrationService extends Service {

  /**
   * 执行完整的数据迁移
   * @param {Object} options 迁移选项
   * @param {Boolean} options.dryRun 是否为试运行（不实际写入数据）
   * @param {String} options.enterpriseId 指定企业ID（可选，不指定则迁移所有）
   * @param {Number} options.batchSize 批处理大小
   */
  async migrateAll(options = {}) {
    const { dryRun = false, enterpriseId = null, batchSize = 100 } = options;
    const { ctx } = this;

    console.log('=== 开始防护用品数据迁移 ===');
    console.log('迁移选项:', options);

    const migrationResult = {
      totalProcessed: 0,
      totalMigrated: 0,
      totalSkipped: 0,
      totalErrors: 0,
      errors: [],
      startTime: new Date(),
      endTime: null,
    };

    try {
      // 构建查询条件
      const query = {};
      if (enterpriseId) {
        query.EnterpriseID = enterpriseId;
      }

      // 获取所有需要迁移的protectiveSuppliesList记录
      const oldLists = await ctx.service.db.find('ProtectiveSuppliesList', query);
      console.log(`找到 ${oldLists.length} 个企业的防护用品清单需要迁移`);

      // 分批处理
      for (let i = 0; i < oldLists.length; i += batchSize) {
        const batch = oldLists.slice(i, i + batchSize);
        const batchResult = await this.migrateBatch(batch, dryRun);

        migrationResult.totalProcessed += batchResult.processed;
        migrationResult.totalMigrated += batchResult.migrated;
        migrationResult.totalSkipped += batchResult.skipped;
        migrationResult.totalErrors += batchResult.errors.length;
        migrationResult.errors.push(...batchResult.errors);

        console.log(`批次 ${Math.floor(i / batchSize) + 1} 完成: 处理 ${batchResult.processed}, 迁移 ${batchResult.migrated}, 跳过 ${batchResult.skipped}, 错误 ${batchResult.errors.length}`);
      }

      migrationResult.endTime = new Date();
      console.log('=== 数据迁移完成 ===');
      console.log('迁移结果:', migrationResult);

      return migrationResult;

    } catch (error) {
      console.error('数据迁移失败:', error);
      migrationResult.endTime = new Date();
      migrationResult.errors.push({
        type: 'MIGRATION_FATAL_ERROR',
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * 迁移一批数据
   */
  async migrateBatch(oldLists, dryRun = false) {
    const { ctx } = this;
    const result = {
      processed: 0,
      migrated: 0,
      skipped: 0,
      errors: [],
    };

    for (const oldList of oldLists) {
      try {
        const listResult = await this.migrateOneList(oldList, dryRun);
        result.processed += 1;
        result.migrated += listResult.migrated;
        result.skipped += listResult.skipped;
        result.errors.push(...listResult.errors);
      } catch (error) {
        result.processed += 1;
        result.errors.push({
          type: 'LIST_MIGRATION_ERROR',
          enterpriseId: oldList.EnterpriseID,
          listId: oldList._id,
          message: error.message,
        });
      }
    }

    return result;
  }

  /**
   * 迁移单个企业的防护用品清单
   */
  async migrateOneList(oldList, dryRun = false) {
    const { ctx } = this;
    const result = {
      migrated: 0,
      skipped: 0,
      errors: [],
    };

    console.log(`开始迁移企业 ${oldList.EnterpriseID} 的防护用品清单`);

    // 遍历list数组中的每个表格
    for (const listItem of oldList.list || []) {
      // 遍历每个表格中的data数组
      for (const productData of listItem.data || []) {
        try {
          // 检查是否已经迁移过
          const existingProduct = await ctx.service.db.findOne('ProtectiveProduct', {
            EnterpriseID: oldList.EnterpriseID,
            warehouseId: oldList.warehouseId || 'public',
            materialCode: productData.materialCode,
            product: productData.product,
            modelNumber: productData.modelNumber,
          });

          if (existingProduct) {
            result.skipped += 1;
            continue;
          }

          // 构建新的产品数据
          const newProductData = this.transformProductData(oldList, productData);

          if (!dryRun) {
            // 实际写入数据
            await ctx.service.db.create('ProtectiveProduct', newProductData);
          }

          result.migrated += 1;

        } catch (error) {
          result.errors.push({
            type: 'PRODUCT_MIGRATION_ERROR',
            enterpriseId: oldList.EnterpriseID,
            productId: productData._id,
            productName: productData.product,
            message: error.message,
          });
        }
      }
    }

    console.log(`企业 ${oldList.EnterpriseID} 迁移完成: 迁移 ${result.migrated}, 跳过 ${result.skipped}, 错误 ${result.errors.length}`);
    return result;
  }

  /**
   * 转换产品数据格式
   */
  transformProductData(oldList, productData) {
    // 处理分类路径ID数组
    let categoryPathIds = [];
    if (productData.categoryPathIds && Array.isArray(productData.categoryPathIds)) {
      categoryPathIds = productData.categoryPathIds;
    } else if (productData.categoryPath && typeof productData.categoryPath === 'string') {
      // 如果categoryPath是字符串，尝试解析为数组
      try {
        const parsed = JSON.parse(productData.categoryPath);
        if (Array.isArray(parsed)) {
          categoryPathIds = parsed;
        }
      } catch (e) {
        // 解析失败，保持为空数组
      }
    }

    return {
      EnterpriseID: oldList.EnterpriseID,
      warehouseId: oldList.warehouseId || 'public',

      // 分类信息
      categoryId: productData.categoryId || null,
      categoryPath: productData.categoryPath || null,
      categoryPathIds,
      categoryName: productData.categoryName || null,

      // 产品基础信息
      materialCode: productData.materialCode || null,
      product: productData.product || '',
      productSpec: productData.productSpec || null,
      modelNumber: productData.modelNumber || null,
      style: productData.style || null,

      // 防护属性
      protectionType: productData.protectionType || null,
      function: productData.function || null,
      harmFactors: Array.isArray(productData.harmFactors) ? productData.harmFactors : [],

      // 技术参数
      APF: productData.APF || null,
      NRR: productData.NRR || null,
      SNR: productData.SNR || null,
      useMethod: productData.useMethod || null,
      accessoryInfo: productData.accessoryInfo || null,
      screenThickness: productData.screenThickness || null,

      // 供应商信息
      vender: productData.vender || null,
      characteristic: productData.characteristic || null,
      industryEnvironment: productData.industryEnvironment || null,
      packing: productData.packing || null,

      // 库存和状态
      surplus: productData.surplus || 0,
      isActive: true,

      // 媒体文件
      picture: productData.picture || null,

      // 版本控制
      version: 1,
    };
  }

  /**
   * 验证迁移结果
   */
  async validateMigration(enterpriseId = null) {
    const { ctx } = this;

    console.log('=== 开始验证迁移结果 ===');

    const query = {};
    if (enterpriseId) {
      query.EnterpriseID = enterpriseId;
    }

    // 统计原始数据
    const oldLists = await ctx.service.db.find('ProtectiveSuppliesList', query);
    let totalOldProducts = 0;
    for (const list of oldLists) {
      for (const listItem of list.list || []) {
        totalOldProducts += (listItem.data || []).length;
      }
    }

    // 统计新数据
    const newProducts = await ctx.service.db.find('ProtectiveProduct', query);
    const totalNewProducts = newProducts.length;

    const validationResult = {
      totalOldProducts,
      totalNewProducts,
      migrationRate: totalOldProducts > 0 ? (totalNewProducts / totalOldProducts * 100).toFixed(2) + '%' : '0%',
      isComplete: totalOldProducts === totalNewProducts,
    };

    console.log('验证结果:', validationResult);
    console.log('=== 迁移验证完成 ===');

    return validationResult;
  }

  /**
   * 回滚迁移（删除所有迁移的数据）
   */
  async rollbackMigration(enterpriseId = null) {
    const { ctx } = this;

    console.log('=== 开始回滚迁移 ===');

    const query = {};
    if (enterpriseId) {
      query.EnterpriseID = enterpriseId;
    }

    const deleteResult = await ctx.service.db.deleteMany('ProtectiveProduct', query);

    console.log(`回滚完成，删除了 ${deleteResult.deletedCount} 条记录`);
    console.log('=== 迁移回滚完成 ===');

    return deleteResult;
  }
}

module.exports = ProtectiveProductMigrationService;
