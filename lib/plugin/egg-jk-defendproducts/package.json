{"name": "egg-jk-defendproducts", "version": "1.0.0", "description": "个人防护用品管理", "private": true, "eggPlugin": {"name": "jk_Defend_Products"}, "keywords": ["egg", "eggPlugin", "egg-plugin"], "dependencies": {"await-stream-ready": "^1.0.1", "egg": "^2.15.1", "egg-cors": "^2.2.3", "egg-mongoose": "^3.2.0", "egg-scripts": "^2.11.0", "exceljs": "^4.3.0", "mz-modules": "^2.1.0", "stream-wormhole": "^1.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"autod": "^3.0.1", "autod-egg": "^1.1.0", "egg-bin": "^4.11.0", "egg-ci": "^1.11.0", "egg-mock": "^3.21.0", "eslint": "^5.13.0", "eslint-config-egg": "^7.1.0"}, "engines": {"node": ">=10.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-template", "stop": "egg-scripts stop --title=egg-server-template", "dev": "egg-bin dev", "debug": "egg-bin debug", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "autod": "autod"}, "ci": {"version": "10"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}